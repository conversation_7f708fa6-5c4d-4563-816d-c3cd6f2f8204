var e=Object.defineProperty,a=Object.defineProperties,l=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,n=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,i=(a,l,t)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[l]=t,o=(e,a)=>{for(var l in a||(a={}))n.call(a,l)&&i(e,l,a[l]);if(t)for(var l of t(a))s.call(a,l)&&i(e,l,a[l]);return e},r=(e,t)=>a(e,l(t)),u=(e,a)=>{var l={};for(var i in e)n.call(e,i)&&a.indexOf(i)<0&&(l[i]=e[i]);if(null!=e&&t)for(var i of t(e))a.indexOf(i)<0&&s.call(e,i)&&(l[i]=e[i]);return l},c=(e,a,l)=>i(e,"symbol"!=typeof a?a+"":a,l),d=(e,a,l)=>new Promise((t,n)=>{var s=e=>{try{o(l.next(e))}catch(a){n(a)}},i=e=>{try{o(l.throw(e))}catch(a){n(a)}},o=e=>e.done?t(e.value):Promise.resolve(e.value).then(s,i);o((l=l.apply(e,a)).next())});import{l as m,_ as v,u as p,a as f,f as g,d as y,I as h,V as x,F as w,B as k,w as _,b}from"./components-BbzPJspU.js";/* empty css              */import{p as C,P as S,g as V}from"./ProxyManagement-B-RgoOEE.js";import{u as P}from"./avatar-CHH9M8bf.js";import{t as D,r as U,f as T,P as I,v as j,x as A,M as N,E as M,A as z,J as $,H as E,K as H,az as O,D as F,u as L,ac as W,L as K,ah as q,R,S as B,c as Q,G as J,h as G,s as Y,aA as X,C as Z,b as ee,aB as ae,n as le,d as te,g as ne,aC as se,i as ie,e as oe,o as re,w as ue,a0 as ce,aD as de}from"./vue-C-lRF7aZ.js";import{k as me,m as ve,n as pe,o as fe,q as ge,r as ye,s as he,c as xe,t as we,u as ke,w as _e,E as be,a as Ce,x as Se,y as Ve,z as Pe,A as De,B as Ue,C as Te,D as Ie,i as je,F as Ae,G as Ne,l as Me,p as ze,H as $e,I as Ee,J as He,K as Oe,L as Fe,M as Le,d as We,N as Ke,O as qe,P as Re,Q as Be,R as Qe,S as Je,T as Ge,U as Ye,V as Xe,W as Ze,X as ea,Y as aa,Z as la,_ as ta,$ as na,a0 as sa,a1 as ia,a2 as oa,a3 as ra,a4 as ua,a5 as ca,a6 as da,a7 as ma,a8 as va}from"./element-plus-DAEtRXw7.js";import{g as pa,i as fa,a as ga,d as ya,b as ha}from"./index-grLkMC3o.js";import"./utils-XWYVm-q4.js";const xa={class:"login-form"},wa={class:"qr-section"},ka={key:0,class:"qr-placeholder"},_a={key:1,class:"qr-display"},ba={class:"qr-code"},Ca=["src"],Sa={class:"qr-tips"},Va={key:0,class:"countdown"},Pa={class:"login-actions"},Da=v(D({__name:"LoginForm",emits:["login-success","close"],setup(e,{expose:a,emit:l}){const t=l,n=U("qrcode"),s=U(!1),i=U(""),o=U(0),r=U([]),u=U([]),c=U({deviceType:"Car",deviceId:"",deviceName:"",proxy:{ProxyIp:"",ProxyPort:1080,ProxyUser:"",ProxyPass:""}}),v=U({username:"",password:"",data62:"",deviceName:"",proxy:{ProxyIp:"",ProxyPort:1080,ProxyUser:"",ProxyPass:""}});let p=null;T(()=>{g()});const f=()=>`CAR_${Date.now().toString().slice(-6)}_${Math.random().toString(36).substr(2,6).toUpperCase()}`,g=()=>{c.value.deviceId=f(),c.value.deviceName=(()=>{const e=["奔驰","宝马","奥迪","特斯拉","蔚来","理想","小鹏","比亚迪","吉利","长城"],a=["智能座舱","车载系统","中控屏","智能终端","车机"],l=["Pro","Max","Plus","Elite","Premium"];return`${e[Math.floor(Math.random()*e.length)]}${a[Math.floor(Math.random()*a.length)]}${l[Math.floor(Math.random()*l.length)]}`})()},y=()=>d(null,null,function*(){c.value.deviceId&&c.value.deviceName||g(),s.value=!0;try{const e={DeviceID:c.value.deviceId,DeviceName:c.value.deviceName,Proxy:c.value.proxy.ProxyIp?{Host:c.value.proxy.ProxyIp,Port:c.value.proxy.ProxyPort,ProxyUser:c.value.proxy.ProxyUser||void 0,ProxyPassword:c.value.proxy.ProxyPass||void 0,Type:"SOCKS5"}:void 0},a=yield m.getQRCodeForDeviceReuse(e);if(1!==a.Code&&!0!==a.Success||!a.Data)throw new Error(a.Message||"生成车载版二维码失败");if(!a.Data.QrBase64)throw new Error("响应中没有找到QrBase64数据");i.value=a.Data.QrBase64,be.success("车载版二维码生成成功，请使用微信扫码登录"),h(a.Data.Uuid)}catch(e){be.error(e.message||"生成二维码失败")}finally{s.value=!1}}),h=e=>{p&&(clearInterval(p),p=null),o.value=120,p=setInterval(()=>d(null,null,function*(){o.value--;try{const l=yield m.checkQRCodeStatus({Uuid:e});if(l.Success&&"登录成功"===l.Message){if(clearInterval(p),be.success("扫码登录成功！正在初始化..."),l.Data&&l.Data.wxid){try{yield m.performSecondAuth(l.Data.wxid),be.success("账号初始化成功！")}catch(a){be.warning("账号初始化失败，请手动重连")}if(c.value.proxy.ProxyIp)try{const e=yield m.setProxy({Wxid:l.Data.wxid,Type:"SOCKS5",Host:c.value.proxy.ProxyIp,Port:c.value.proxy.Port,User:c.value.proxy.ProxyUser||"",Password:c.value.proxy.ProxyPassword||""});e.Success?be.success("代理已自动配置"):be.warning(`代理设置失败: ${e.Message}`)}catch(a){be.warning("代理设置失败")}const e={wxid:l.Data.wxid,nickname:l.Data.nickname||c.value.deviceName,avatar:l.Data.avatar||"",status:"online",deviceType:"Car",deviceId:c.value.deviceId,deviceName:c.value.deviceName,loginTime:new Date,proxy:c.value.proxy.ProxyIp?c.value.proxy:void 0};t("login-success",e)}return}if(l.Success&&l.Data){const e=l.Data;if(e.expiredTime<=0)clearInterval(p),be.error("二维码已过期，请重新生成"),w();else if(0===e.status);else if(1===e.status){const a=e.nickName||"用户";be.info(`${a}已扫码，请在手机上确认登录`)}else 4===e.status&&(clearInterval(p),be.warning("用户取消登录"),w())}}catch(a){}o.value<=0&&(clearInterval(p),be.error("二维码已过期，请重新生成"),w())}),1e3)},x=()=>{p&&(clearInterval(p),p=null)},w=()=>{i.value="",o.value=0,x()},k=()=>d(null,null,function*(){var e;if(v.value.username&&v.value.password){s.value=!0;try{const a={UserName:v.value.username,Password:v.value.password,Data62:v.value.data62,DeviceName:v.value.deviceName||"微信机器人",Proxy:v.value.proxy.ProxyIp?{Host:v.value.proxy.ProxyIp,Port:v.value.proxy.ProxyPort,ProxyUser:v.value.proxy.ProxyUser||void 0,ProxyPassword:v.value.proxy.ProxyPass||void 0,Type:"SOCKS5"}:void 0},l=yield m.loginWithData62(a);if(!l.Success||!(null==(e=l.Data)?void 0:e.wxid))throw new Error(l.Message||"登录失败");{const e={wxid:l.Data.wxid,nickname:l.Data.nickname||v.value.username,avatar:l.Data.avatar||"",status:"online",deviceType:"Data62",deviceId:f(),deviceName:v.value.deviceName||"微信机器人",loginTime:new Date,proxy:v.value.proxy.ProxyIp?v.value.proxy:void 0};t("login-success",e)}}catch(a){be.error(a.message||"登录失败")}finally{s.value=!1}}else be.error("请填写微信号和密码")});return I(()=>{x()}),a({clearTimer:x,resetQRCode:w}),(e,a)=>{const l=ye,t=ge,d=fe,m=xe,p=he,f=_e,h=ke,x=we,_=pe,b=ve,C=me;return A(),j("div",xa,[N(C,{modelValue:n.value,"onUpdate:modelValue":a[17]||(a[17]=e=>n.value=e),class:"login-tabs"},{default:M(()=>[N(b,{label:"二维码登录",name:"qrcode"},{default:M(()=>[N(_,{model:c.value,"label-width":"100px",class:"qr-form"},{default:M(()=>[N(d,{label:"设备类型"},{default:M(()=>[N(t,{modelValue:c.value.deviceType,"onUpdate:modelValue":a[0]||(a[0]=e=>c.value.deviceType=e),placeholder:"选择设备类型"},{default:M(()=>[N(l,{label:"车载微信",value:"Car"})]),_:1},8,["modelValue"])]),_:1}),N(d,{label:"设备ID"},{default:M(()=>[N(p,{modelValue:c.value.deviceId,"onUpdate:modelValue":a[1]||(a[1]=e=>c.value.deviceId=e),placeholder:"自动生成随机设备ID"},{append:M(()=>[N(m,{onClick:g},{default:M(()=>a[18]||(a[18]=[$("随机生成",-1)])),_:1,__:[18]})]),_:1},8,["modelValue"])]),_:1}),N(d,{label:"设备名称"},{default:M(()=>[N(p,{modelValue:c.value.deviceName,"onUpdate:modelValue":a[2]||(a[2]=e=>c.value.deviceName=e),placeholder:"自动生成随机设备名称"},null,8,["modelValue"])]),_:1}),N(x,{modelValue:r.value,"onUpdate:modelValue":a[7]||(a[7]=e=>r.value=e)},{default:M(()=>[N(h,{title:"代理配置（可选）",name:"proxy"},{default:M(()=>[N(d,{label:"代理IP"},{default:M(()=>[N(p,{modelValue:c.value.proxy.ProxyIp,"onUpdate:modelValue":a[3]||(a[3]=e=>c.value.proxy.ProxyIp=e),placeholder:"代理服务器IP"},null,8,["modelValue"])]),_:1}),N(d,{label:"代理端口"},{default:M(()=>[N(f,{modelValue:c.value.proxy.ProxyPort,"onUpdate:modelValue":a[4]||(a[4]=e=>c.value.proxy.ProxyPort=e),min:1,max:65535},null,8,["modelValue"])]),_:1}),N(d,{label:"用户名"},{default:M(()=>[N(p,{modelValue:c.value.proxy.ProxyUser,"onUpdate:modelValue":a[5]||(a[5]=e=>c.value.proxy.ProxyUser=e),placeholder:"代理用户名（可选）"},null,8,["modelValue"])]),_:1}),N(d,{label:"密码"},{default:M(()=>[N(p,{modelValue:c.value.proxy.ProxyPass,"onUpdate:modelValue":a[6]||(a[6]=e=>c.value.proxy.ProxyPass=e),type:"password",placeholder:"代理密码（可选）"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"]),z("div",wa,[i.value?(A(),j("div",_a,[z("div",ba,[z("img",{src:i.value,alt:"登录二维码"},null,8,Ca)]),z("div",Sa,[a[21]||(a[21]=z("p",null,"请使用微信扫描二维码登录",-1)),o.value>0?(A(),j("p",Va,H(o.value)+"秒后过期，请及时扫码",1)):E("",!0),N(m,{size:"small",onClick:w},{default:M(()=>a[20]||(a[20]=[$("重新生成",-1)])),_:1,__:[20]})])])):(A(),j("div",ka,[N(m,{type:"primary",loading:s.value,onClick:y},{default:M(()=>a[19]||(a[19]=[$(" 生成二维码 ",-1)])),_:1,__:[19]},8,["loading"])]))])]),_:1},8,["model"])]),_:1}),N(b,{label:"密码登录",name:"password"},{default:M(()=>[N(_,{model:v.value,"label-width":"100px",class:"password-form"},{default:M(()=>[N(d,{label:"微信号"},{default:M(()=>[N(p,{modelValue:v.value.username,"onUpdate:modelValue":a[8]||(a[8]=e=>v.value.username=e),placeholder:"输入微信号"},null,8,["modelValue"])]),_:1}),N(d,{label:"密码"},{default:M(()=>[N(p,{modelValue:v.value.password,"onUpdate:modelValue":a[9]||(a[9]=e=>v.value.password=e),type:"password",placeholder:"输入密码"},null,8,["modelValue"])]),_:1}),N(d,{label:"Data62"},{default:M(()=>[N(p,{modelValue:v.value.data62,"onUpdate:modelValue":a[10]||(a[10]=e=>v.value.data62=e),type:"textarea",rows:3,placeholder:"输入Data62数据"},null,8,["modelValue"])]),_:1}),N(d,{label:"设备名称"},{default:M(()=>[N(p,{modelValue:v.value.deviceName,"onUpdate:modelValue":a[11]||(a[11]=e=>v.value.deviceName=e),placeholder:"输入设备名称"},null,8,["modelValue"])]),_:1}),N(x,{modelValue:u.value,"onUpdate:modelValue":a[16]||(a[16]=e=>u.value=e)},{default:M(()=>[N(h,{title:"代理配置（可选）",name:"proxy"},{default:M(()=>[N(d,{label:"代理IP"},{default:M(()=>[N(p,{modelValue:v.value.proxy.ProxyIp,"onUpdate:modelValue":a[12]||(a[12]=e=>v.value.proxy.ProxyIp=e),placeholder:"代理服务器IP"},null,8,["modelValue"])]),_:1}),N(d,{label:"代理端口"},{default:M(()=>[N(f,{modelValue:v.value.proxy.ProxyPort,"onUpdate:modelValue":a[13]||(a[13]=e=>v.value.proxy.ProxyPort=e),min:1,max:65535},null,8,["modelValue"])]),_:1}),N(d,{label:"用户名"},{default:M(()=>[N(p,{modelValue:v.value.proxy.ProxyUser,"onUpdate:modelValue":a[14]||(a[14]=e=>v.value.proxy.ProxyUser=e),placeholder:"代理用户名（可选）"},null,8,["modelValue"])]),_:1}),N(d,{label:"密码"},{default:M(()=>[N(p,{modelValue:v.value.proxy.ProxyPass,"onUpdate:modelValue":a[15]||(a[15]=e=>v.value.proxy.ProxyPass=e),type:"password",placeholder:"代理密码（可选）"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"]),z("div",Pa,[N(m,{type:"primary",loading:s.value,onClick:k},{default:M(()=>a[22]||(a[22]=[$(" 登录 ",-1)])),_:1,__:[22]},8,["loading"])])]),_:1},8,["model"])]),_:1})]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-ad265a48"]]),Ua={class:"friend-management"},Ta={class:"welcome-content"},Ia={class:"feature-list"},ja={class:"feature-item"},Aa={class:"feature-item"},Na={class:"feature-item"},Ma={key:0,class:"search-result"},za={class:"user-card"},$a={class:"user-info"},Ea={class:"nickname"},Ha={class:"wxid"},Oa={key:0},Fa={class:"wxid-info"},La={key:0,class:"region"},Wa={class:"actions"},Ka={class:"friend-actions"},qa={key:0,class:"add-friend-content"},Ra={class:"friend-info"},Ba={class:"info"},Qa={class:"nickname"},Ja={class:"wxid"},Ga={key:0},Ya={class:"wxid-info"},Xa={key:0,class:"signature"},Za=v(D({__name:"FriendManagement",props:{account:{}},setup(e){const a=2,l=3,t=[{label:"通过微信号搜索",value:l},{label:"通过QQ添加",value:1},{label:"通过邮箱添加",value:2},{label:"通过通讯录添加",value:13},{label:"通过群聊添加",value:14},{label:"通过手机号添加",value:15},{label:"通过附近的人",value:18},{label:"通过二维码添加",value:30}],n=e;p();const s=f(),i=O(),o=U({type:"wxid",keyword:""}),r=U(null),u=U(!1),c=U(!1),m=U(!1),v=U(!1),y=U({verifyContent:"你好，我想加你为好友",scene:l}),h=U(!1),x=U({remark:""}),w=U(null),k=U({visible:!1,x:0,y:0,friend:null}),_=e=>{if(!e.nickname||!e.wxid)return"not-exist";const a=e.antispamTicket||e.AntispamTicket||"";return a.includes("@stranger")||a?"stranger":"friend"},b=e=>{switch(_(e)){case"not-exist":return"用户不存在";case"friend":return"已是好友";case"stranger":return"可以添加";default:return"未知状态"}},C=e=>{switch(_(e)){case"not-exist":return"danger";case"friend":return"success";case"stranger":return"warning";default:return"info"}},S=()=>d(null,null,function*(){var e;if(r.value&&(null==(e=n.account)?void 0:e.wxid))if("not-exist"!==_(r.value)){m.value=!0;try{const e={wxid:r.value.wxid,nickname:r.value.nickname,avatar:r.value.avatar||"",remark:"",isOnline:!0},a=s.createOrGetSession(e);s.setCurrentSession(a.id),yield i.push("/dashboard?tab=chat"),be.success("已打开聊天窗口")}catch(a){be.error(a.message||"打开聊天失败")}finally{m.value=!1}}else be.error("用户不存在，无法发送消息");else be.error("缺少必要信息")}),V=()=>d(null,null,function*(){var e,a,l,t,s,i,c,d;if(o.value.keyword.trim())if(null==(e=n.account)?void 0:e.wxid){u.value=!0;try{const e={Wxid:n.account.wxid,ToUserName:o.value.keyword,FromScene:0,SearchScene:1},u=yield g.searchContact(e);if(!u.Success||!u.Data)throw new Error(u.Message||"未找到用户");{let e="";u.Data.Alias&&u.Data.Alias.trim()?e=u.Data.Alias:"wxid"===o.value.type&&o.value.keyword.trim()?e=o.value.keyword:(null==(a=u.Data.QuanPin)?void 0:a.string)&&u.Data.QuanPin.string.trim()&&!u.Data.QuanPin.string.startsWith("wxid_")&&(e=u.Data.QuanPin.string);const n=(null==(t=null==(l=u.Data.UserName)?void 0:l.string)?void 0:t.includes("@stranger"))||!1;r.value={wxid:n?(null==(s=u.Data.Pyinitial)?void 0:s.string)||o.value.keyword:(null==(i=u.Data.UserName)?void 0:i.string)||o.value.keyword,nickname:(null==(c=u.Data.NickName)?void 0:c.string)||"未知用户",alias:e,avatar:u.Data.BigHeadImgUrl||u.Data.SmallHeadImgUrl||"",region:`${u.Data.Country||""} ${u.Data.City||""}`.trim()||"未知",signature:u.Data.Signature||"",sex:u.Data.Sex||0,verifyFlag:u.Data.VerifyFlag||0,v1:(null==(d=u.Data.UserName)?void 0:d.string)||"",v2:u.Data.AntispamTicket||"",antispamTicket:u.Data.AntispamTicket||""},be.success("搜索完成")}}catch(m){be.error(m.message||"搜索失败"),r.value=null}finally{u.value=!1}}else be.error("请先登录账号");else be.warning("请输入搜索内容")}),P=()=>{r.value?v.value=!0:be.warning("请先搜索用户")},D=()=>d(null,null,function*(){var e;if(r.value&&(null==(e=n.account)?void 0:e.wxid))if(y.value.verifyContent.trim())if(r.value.v1&&r.value.v2){c.value=!0;try{const e=((e,t,n,s,i=l,o=a)=>({Wxid:e.trim(),V1:t.trim(),V2:n.trim(),Opcode:o,Scene:i,VerifyContent:s.trim()}))(n.account.wxid,r.value.v1,r.value.v2,y.value.verifyContent,y.value.scene,a),t=(e=>{var a,l,t,n;const s=[];return(null==(a=e.Wxid)?void 0:a.trim())||s.push("发送方微信ID不能为空"),(null==(l=e.V1)?void 0:l.trim())||s.push("V1参数不能为空"),(null==(t=e.V2)?void 0:t.trim())||s.push("V2参数不能为空"),(null==(n=e.VerifyContent)?void 0:n.trim())||s.push("验证消息不能为空"),e.V1&&!e.V1.startsWith("v3_")&&s.push("V1参数格式不正确，应该以v3_开头"),e.V2&&!e.V2.startsWith("v4_")&&s.push("V2参数格式不正确，应该以v4_开头"),{isValid:0===s.length,errors:s}})(e);if(!t.isValid)return void be.error(`参数验证失败：${t.errors.join(", ")}`);const s=yield g.sendFriendRequest(e);if(!s.Success)throw new Error(s.Message||"发送好友请求失败");be.success("好友请求已发送"),v.value=!1,r.value=null,o.value.keyword="",y.value.verifyContent="你好，我想加你为好友",y.value.scene=l}catch(t){be.error(t.message||"添加好友失败")}finally{c.value=!1}}else be.error("搜索结果缺少必要参数，请重新搜索");else be.warning("请输入打招呼内容");else be.error("缺少必要信息")}),T=e=>null==e?"":"string"==typeof e?e:"number"==typeof e?String(e):"object"==typeof e?0===Object.keys(e).length?"":void 0!==e.string&&null!==e.string&&""!==e.string?String(e.string):void 0!==e.value&&null!==e.value&&""!==e.value?String(e.value):void 0!==e.text&&null!==e.text&&""!==e.text?String(e.text):"":String(e),I=e=>{},Q=()=>{k.value.visible=!1,k.value.friend=null},J=e=>{const a=k.value.friend;if(a){switch(e){case"chat":case"delete":break;case"remark":w.value=a,x.value.remark=a.remark||"",h.value=!0}Q()}},G=()=>d(null,null,function*(){if(w.value)try{be.success("备注修改成功"),h.value=!1}catch(e){be.error("修改备注失败")}});return(e,a)=>{const l=Ce,n=Ve,s=Ue,i=ye,d=ge,p=fe,f=he,g=xe,U=pe,O=Te,Y=Ie,X=je;return A(),j("div",Ua,[r.value?E("",!0):(A(),F(s,{key:0,class:"welcome-card",shadow:"never"},{default:M(()=>[z("div",Ta,[N(l,{class:"welcome-icon",size:"64"},{default:M(()=>[N(L(Se))]),_:1}),a[16]||(a[16]=z("h3",null,"好友管理",-1)),a[17]||(a[17]=z("p",null,"通过下方搜索功能查找并添加好友，或向已有好友发送消息",-1)),N(n),z("div",Ia,[z("div",ja,[N(l,null,{default:M(()=>[N(L(Pe))]),_:1}),a[13]||(a[13]=z("span",null,"支持微信号、手机号、QQ号搜索",-1))]),z("div",Aa,[N(l,null,{default:M(()=>[N(L(Se))]),_:1}),a[14]||(a[14]=z("span",null,"快速添加陌生人为好友",-1))]),z("div",Na,[N(l,null,{default:M(()=>[N(L(De))]),_:1}),a[15]||(a[15]=z("span",null,"直接向好友发送消息",-1))])])])]),_:1})),N(s,{class:"search-card",shadow:"never"},{header:M(()=>a[18]||(a[18]=[z("div",{class:"card-header"},[z("span",null,"添加好友")],-1)])),default:M(()=>[N(U,{model:o.value,inline:"",class:"search-form"},{default:M(()=>[N(p,{label:"搜索方式"},{default:M(()=>[N(d,{modelValue:o.value.type,"onUpdate:modelValue":a[0]||(a[0]=e=>o.value.type=e),placeholder:"选择搜索方式",style:{width:"120px"}},{default:M(()=>[N(i,{label:"微信号",value:"wxid"}),N(i,{label:"手机号",value:"phone"}),N(i,{label:"QQ号",value:"qq"})]),_:1},8,["modelValue"])]),_:1}),N(p,{label:"搜索内容"},{default:M(()=>[N(f,{modelValue:o.value.keyword,"onUpdate:modelValue":a[1]||(a[1]=e=>o.value.keyword=e),placeholder:"输入搜索内容",style:{width:"200px"},onKeyup:W(V,["enter"])},null,8,["modelValue"])]),_:1}),N(p,null,{default:M(()=>[N(g,{type:"primary",loading:u.value,onClick:V,style:{width:"80px"}},{default:M(()=>[u.value?E("",!0):(A(),F(l,{key:0},{default:M(()=>[N(L(Pe))]),_:1})),$(" "+H(u.value?"搜索中":"搜索"),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"]),r.value?(A(),j("div",Ma,[N(n,{"content-position":"left"},{default:M(()=>a[19]||(a[19]=[$("搜索结果",-1)])),_:1,__:[19]}),z("div",za,[N(O,{src:r.value.avatar,size:50,onError:I},{default:M(()=>[$(H(T(r.value.nickname).charAt(0)||"?"),1)]),_:1},8,["src"]),z("div",$a,[z("div",Ea,[$(H(T(r.value.nickname))+" ",1),1===r.value.sex?(A(),F(Y,{key:0,type:"info",size:"small",style:{"margin-left":"8px"}},{default:M(()=>a[20]||(a[20]=[$("男",-1)])),_:1,__:[20]})):E("",!0),2===r.value.sex?(A(),F(Y,{key:1,type:"warning",size:"small",style:{"margin-left":"8px"}},{default:M(()=>a[21]||(a[21]=[$("女",-1)])),_:1,__:[21]})):E("",!0),r.value.verifyFlag>0?(A(),F(Y,{key:2,type:"success",size:"small",style:{"margin-left":"8px"}},{default:M(()=>a[22]||(a[22]=[$("认证",-1)])),_:1,__:[22]})):E("",!0)]),z("div",Ha,[T(r.value.alias).trim()?(A(),j("span",Oa,"微信号："+H(T(r.value.alias)),1)):E("",!0),z("span",Fa,"["+H(T(r.value.wxid))+"]",1)]),T(r.value.region)?(A(),j("div",La,"地区："+H(T(r.value.region)),1)):E("",!0)]),z("div",Wa,[N(Y,{type:C(r.value),size:"large",style:{"margin-right":"10px"}},{default:M(()=>[$(H(b(r.value)),1)]),_:1},8,["type"])])])])):E("",!0)]),_:1}),r.value?(A(),F(s,{key:1,class:"friend-actions-card",shadow:"never"},{header:M(()=>a[23]||(a[23]=[z("div",{class:"card-header"},[z("span",null,"好友操作")],-1)])),default:M(()=>[z("div",Ka,[N(g,{type:"primary",loading:m.value,onClick:S,disabled:"not-exist"===_(r.value)},{default:M(()=>[N(l,null,{default:M(()=>[N(L(De))]),_:1}),a[24]||(a[24]=$(" 发送消息 ",-1))]),_:1,__:[24]},8,["loading","disabled"]),"stranger"===_(r.value)?(A(),F(g,{key:0,type:"success",loading:c.value,onClick:P},{default:M(()=>[N(l,null,{default:M(()=>[N(L(Se))]),_:1}),a[25]||(a[25]=$(" 添加好友 ",-1))]),_:1,__:[25]},8,["loading"])):E("",!0),N(Y,{type:C(r.value),size:"large",style:{"margin-left":"10px"}},{default:M(()=>[$(H(b(r.value)),1)]),_:1},8,["type"])])]),_:1})):E("",!0),N(X,{modelValue:v.value,"onUpdate:modelValue":a[5]||(a[5]=e=>v.value=e),title:"添加好友",width:"500px"},{footer:M(()=>[N(g,{onClick:a[4]||(a[4]=e=>v.value=!1)},{default:M(()=>a[26]||(a[26]=[$("取消",-1)])),_:1,__:[26]}),N(g,{type:"primary",loading:c.value,onClick:D},{default:M(()=>[N(l,null,{default:M(()=>[N(L(Se))]),_:1}),a[27]||(a[27]=$(" 发送好友请求 ",-1))]),_:1,__:[27]},8,["loading"])]),default:M(()=>[r.value?(A(),j("div",qa,[z("div",Ra,[N(O,{src:r.value.avatar,size:60},{default:M(()=>[N(l,null,{default:M(()=>[N(L(Se))]),_:1})]),_:1},8,["src"]),z("div",Ba,[z("div",Qa,H(r.value.nickname),1),z("div",Ja,[T(r.value.alias).trim()?(A(),j("span",Ga,"微信号："+H(T(r.value.alias)),1)):E("",!0),z("span",Ya,"["+H(T(r.value.wxid))+"]",1)]),r.value.signature?(A(),j("div",Xa,H(r.value.signature),1)):E("",!0)])]),N(U,{model:y.value,"label-width":"100px",class:"add-friend-form"},{default:M(()=>[N(p,{label:"打招呼内容",required:""},{default:M(()=>[N(f,{modelValue:y.value.verifyContent,"onUpdate:modelValue":a[2]||(a[2]=e=>y.value.verifyContent=e),type:"textarea",rows:4,placeholder:"请输入打招呼内容...",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1}),N(p,{label:"验证来源"},{default:M(()=>[N(d,{modelValue:y.value.scene,"onUpdate:modelValue":a[3]||(a[3]=e=>y.value.scene=e),placeholder:"选择验证来源"},{default:M(()=>[(A(),j(K,null,q(t,e=>N(i,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])])):E("",!0)]),_:1},8,["modelValue"]),N(X,{modelValue:h.value,"onUpdate:modelValue":a[8]||(a[8]=e=>h.value=e),title:"修改备注",width:"400px"},{footer:M(()=>[N(g,{onClick:a[7]||(a[7]=e=>h.value=!1)},{default:M(()=>a[28]||(a[28]=[$("取消",-1)])),_:1,__:[28]}),N(g,{type:"primary",onClick:G},{default:M(()=>a[29]||(a[29]=[$("确定",-1)])),_:1,__:[29]})]),default:M(()=>[N(U,{model:x.value,"label-width":"80px"},{default:M(()=>[N(p,{label:"好友"},{default:M(()=>{var e;return[z("span",null,H(null==(e=w.value)?void 0:e.nickname),1)]}),_:1}),N(p,{label:"备注名"},{default:M(()=>[N(f,{modelValue:x.value.remark,"onUpdate:modelValue":a[6]||(a[6]=e=>x.value.remark=e),placeholder:"输入备注名"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),k.value.visible?(A(),j("div",{key:2,class:"context-menu",style:B({left:k.value.x+"px",top:k.value.y+"px"}),onClick:a[12]||(a[12]=R(()=>{},["stop"]))},[z("div",{class:"context-menu-item",onClick:a[9]||(a[9]=e=>J("chat"))},[N(l,null,{default:M(()=>[N(L(De))]),_:1}),a[30]||(a[30]=z("span",null,"发送消息",-1))]),z("div",{class:"context-menu-item",onClick:a[10]||(a[10]=e=>J("remark"))},[N(l,null,{default:M(()=>[N(L(Ae))]),_:1}),a[31]||(a[31]=z("span",null,"修改备注",-1))]),a[33]||(a[33]=z("div",{class:"context-menu-divider"},null,-1)),z("div",{class:"context-menu-item danger",onClick:a[11]||(a[11]=e=>J("delete"))},[N(l,null,{default:M(()=>[N(L(Ne))]),_:1}),a[32]||(a[32]=z("span",null,"删除好友",-1))])],4)):E("",!0),k.value.visible?(A(),j("div",{key:3,class:"context-menu-overlay",onClick:Q,onContextmenu:R(Q,["prevent"])},null,32)):E("",!0)])}}}),[["__scopeId","data-v-132bdea6"]]),el={class:"emoji-image-wrapper"},al={key:0,class:"emoji-loading"},ll={key:1,class:"emoji-error"},tl={key:2,class:"emoji-success"},nl=["src"],sl={key:3,class:"emoji-unknown"},il=v(D({__name:"EmojiImage",props:{emojiUrl:{},emojiThumbUrl:{},emojiExternUrl:{},emojiAesKey:{},emojiMd5:{},emojiWidth:{},emojiHeight:{},maxWidth:{default:200},maxHeight:{default:200}},setup(e){const a=e,l=f(),t=U(!0),n=U(!1),s=U(""),i=Q(()=>{const e=a.maxWidth,l=a.maxHeight;let t=a.emojiWidth||120,n=a.emojiHeight||120;if(t>e||n>l){const a=Math.min(e/t,l/n);t=Math.floor(t*a),n=Math.floor(n*a)}return t<60&&(t=60),n<60&&(n=60),{width:`${t}px`,height:`${n}px`,maxWidth:`${e}px`,maxHeight:`${l}px`,borderRadius:"4px",cursor:"pointer"}}),r=(e,...a)=>d(null,[e,...a],function*(e,a={}){const l=yield fetch(e,o({mode:"cors"},a));if(l.ok){const e=yield l.blob();if(0===e.size)throw new Error("Blob大小为0");return!e.type||e.type.startsWith("image/"),URL.createObjectURL(e)}throw new Error(`HTTP ${l.status}`)}),u=()=>{a.emojiUrl&&a.emojiUrl!==s.value&&window.open(a.emojiUrl,"_blank")},c=()=>{},m=e=>d(null,null,function*(){if(s.value&&s.value.startsWith("blob:")){const e=a.emojiThumbUrl||a.emojiUrl;if(e)return URL.revokeObjectURL(s.value),void(s.value=e)}n.value=!0});return T(()=>{d(null,null,function*(){const e=a.emojiUrl||a.emojiThumbUrl,i=a.emojiThumbUrl||a.emojiUrl;if(!e)return n.value=!0,void(t.value=!1);const o=[e];i&&i!==e&&o.push(i),a.emojiExternUrl&&a.emojiExternUrl!==e&&a.emojiExternUrl!==i&&o.push(a.emojiExternUrl);for(const c of o){const e=[()=>{return e=c,new Promise((a,l)=>{const t=new Image;t.crossOrigin="anonymous",t.onload=()=>a(e),t.onerror=l,t.src=e});var e},()=>r(c,{headers:{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",Referer:"https://wx.qq.com/"}}),()=>r(c),()=>{return e=c,d(null,null,function*(){var t;if(!a.emojiAesKey||!a.emojiMd5)return null;try{const n=e.match(/filekey=([^&]+)/);if(!n)return null;const s=yield y({Wxid:(null==(t=l.currentSession)?void 0:t.sessionId)||"",FileAesKey:a.emojiAesKey,FileNo:n[1]});return s.data&&s.data.length>0?`data:image/png;base64,${s.data}`:null}catch(n){return null}});var e},()=>Promise.resolve(c)];for(let a=0;a<e.length;a++)try{const l=yield e[a]();if(null===l)continue;return s.value=l,t.value=!1,void(n.value=!1)}catch(u){}}n.value=!0,t.value=!1})}),I(()=>{s.value&&s.value.startsWith("blob:")&&URL.revokeObjectURL(s.value)}),(e,a)=>{const l=Ce;return A(),j("div",el,[t.value?(A(),j("div",al,[N(l,{class:"is-loading"},{default:M(()=>[N(L(Me))]),_:1}),a[0]||(a[0]=z("div",{style:{"font-size":"10px","margin-top":"4px"}},"加载中...",-1))])):n.value?(A(),j("div",ll,[N(l,{class:"emoji-error-icon"},{default:M(()=>[N(L(ze))]),_:1}),a[1]||(a[1]=z("span",{class:"emoji-error-text"},"[表情]",-1)),a[2]||(a[2]=z("div",{style:{"font-size":"10px","margin-top":"4px"}},"加载失败",-1))])):s.value?(A(),j("div",tl,[z("img",{src:s.value,style:B(i.value),class:"emoji-content",onClick:u,onLoad:c,onError:m,alt:"表情"},null,44,nl)])):(A(),j("div",sl,a[3]||(a[3]=[z("div",{style:{"font-size":"12px",color:"#999"}},"未知状态",-1)])))])}}}),[["__scopeId","data-v-058f08ab"]]),ol={key:0,class:"message-time"},rl={key:1,class:"message-wrapper message-system-wrapper"},ul={class:"message-content message-system-content"},cl={class:"message-system"},dl={key:2,class:"message-wrapper message-from-other"},ml={class:"message-avatar"},vl={class:"message-content-area"},pl={key:0,class:"sender-info"},fl={class:"sender-name"},gl={key:0,class:"message-text"},yl={key:1,class:"message-image"},hl={key:2,class:"message-video"},xl={key:3,class:"message-file"},wl={key:4,class:"message-link"},kl={class:"link-content"},_l={class:"link-title"},bl={class:"link-url"},Cl={key:0,class:"link-thumb"},Sl=["src"],Vl={key:5,class:"message-emoji"},Pl={key:1,class:"emoji-placeholder"},Dl={class:"emoji-text"},Ul={key:1,class:"message-retry"},Tl={key:3,class:"message-wrapper message-from-me"},Il={class:"message-content-area"},jl={key:0,class:"message-text"},Al={key:1,class:"message-image"},Nl={key:2,class:"message-file"},Ml={key:3,class:"message-video"},zl={key:4,class:"message-emoji"},$l={key:1,class:"emoji-placeholder"},El={class:"emoji-text"},Hl={key:0,class:"message-retry"},Ol={class:"message-avatar"},Fl=v(D({__name:"MessageItem",props:{message:{},showTime:{type:Boolean,default:!1},avatar:{default:""},avatarText:{default:""},myAvatar:{default:""},myAvatarText:{default:"我"}},emits:["retry","contextmenu"],setup(e,{emit:a}){const l=e,t=f(),n=p(),s=Q(()=>{const e=l.message.id;if(!e)return 0;if("number"==typeof e)return e;const a=parseInt(e.toString(),10);return isNaN(a)?0:a}),i=a,o=Q(()=>{const e=[];return l.message.fromMe?e.push("message-from-me"):e.push("message-from-other"),"system"===l.message.type&&e.push("message-system-type"),e.join(" ")}),r=Q(()=>{const e=[`message-${l.message.type}`];return l.message.fromMe?e.push("content-from-me"):e.push("content-from-other"),"failed"===l.message.status&&e.push("content-failed"),e.join(" ")});function u(e){try{const a=e instanceof Date?e:new Date(e);return Number.isNaN(a.getTime())?"时间无效":new Intl.DateTimeFormat("zh-CN",{hour:"2-digit",minute:"2-digit"}).format(a)}catch(a){return"时间错误"}}function c(){i("retry",l.message)}function d(e){e.preventDefault(),i("contextmenu",e,l.message)}function m(){return l.message.isGroupMessage&&l.message.actualSenderName?l.message.actualSenderName:l.message.actualSender?l.message.actualSender:"未知用户"}function v(e,a){var l;return{jpg:"image/jpeg",jpeg:"image/jpeg",png:"image/png",gif:"image/gif",bmp:"image/bmp",webp:"image/webp",svg:"image/svg+xml",pdf:"application/pdf",doc:"application/msword",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",xls:"application/vnd.ms-excel",xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",ppt:"application/vnd.ms-powerpoint",pptx:"application/vnd.openxmlformats-officedocument.presentationml.presentation",txt:"text/plain",mp4:"video/mp4",avi:"video/x-msvideo",mov:"video/quicktime",wmv:"video/x-ms-wmv",flv:"video/x-flv",mkv:"video/x-matroska",webm:"video/webm",mp3:"audio/mpeg",wav:"audio/wav",flac:"audio/flac",aac:"audio/aac",zip:"application/zip",rar:"application/x-rar-compressed","7z":"application/x-7z-compressed",tar:"application/x-tar",gz:"application/gzip"}[a||(e?null==(l=e.split(".").pop())?void 0:l.toLowerCase():"")||""]||"application/octet-stream"}function g(e){if(e)return e.url?e.url:e.path?e.path:void e.cdnUrl}function y(){var e;return l.message.fromMe?(null==(e=n.currentAccount)?void 0:e.wxid)||"":l.message.actualSender||l.message.sessionId||""}function k(){var e;if(null==(e=l.message.fileData)?void 0:e.originalContent){const e=l.message.fileData.originalContent.match(/<appmsg appid="([^"]*)"/);if(e)return e[1]}return""}return(e,a)=>{var l,i,p,f,_,b,C,S,V,P,D,U,T,I,O,W,K,q,R,B,Q,G,Y,X,Z;const ee=Te,ae=Ce,le=xe;return A(),j("div",{class:J(["message-item",o.value]),onContextmenu:d},[e.showTime?(A(),j("div",ol,H(u(e.message.timestamp)),1)):E("",!0),"system"===e.message.type?(A(),j("div",rl,[z("div",ul,[z("div",cl,H(e.message.content),1)])])):e.message.fromMe?(A(),j("div",Tl,[z("div",Il,[z("div",{class:J(["message-content",r.value])},["text"===e.message.type?(A(),j("div",jl,H(e.message.content),1)):"image"===e.message.type?(A(),j("div",Al,[L(t).currentSession?(A(),F(h,{key:0,"msg-id":s.value,wxid:null==(O=L(n).currentAccount)?void 0:O.wxid,"to-wxid":L(t).currentSession.id,"aes-key":e.message.imageAesKey,md5:e.message.imageMd5,"data-len":e.message.imageDataLen,"compress-type":e.message.imageCompressType,"image-data":e.message.imageData,"image-path":e.message.imagePath,"cdn-file-aes-key":e.message.imageCdnFileAesKey,"cdn-file-no":e.message.imageCdnFileNo},null,8,["msg-id","wxid","to-wxid","aes-key","md5","data-len","compress-type","image-data","image-path","cdn-file-aes-key","cdn-file-no"])):E("",!0)])):"file"===e.message.type?(A(),j("div",Nl,[N(w,{"file-name":(null==(W=e.message.fileData)?void 0:W.name)||"未知文件","file-size":(null==(K=e.message.fileData)?void 0:K.size)||0,"file-url":g(e.message.fileData),"mime-type":v(null==(q=e.message.fileData)?void 0:q.name,null==(R=e.message.fileData)?void 0:R.ext),"cdn-url":null==(B=e.message.fileData)?void 0:B.cdnUrl,"aes-key":null==(Q=e.message.fileData)?void 0:Q.aesKey,"attach-id":null==(G=e.message.fileData)?void 0:G.attachId,wxid:null==(Y=L(n).currentAccount)?void 0:Y.wxid,"user-name":y(),"app-id":k(),"original-content":null==(X=e.message.fileData)?void 0:X.originalContent,"message-status":e.message.status,"from-me":e.message.fromMe},null,8,["file-name","file-size","file-url","mime-type","cdn-url","aes-key","attach-id","wxid","user-name","app-id","original-content","message-status","from-me"])])):"video"===e.message.type?(A(),j("div",Ml,[L(t).currentSession?(A(),F(x,{key:0,"msg-id":s.value,wxid:null==(Z=L(n).currentAccount)?void 0:Z.wxid,"to-wxid":L(t).currentSession.id,"aes-key":e.message.videoAesKey,md5:e.message.videoMd5,"new-md5":e.message.videoNewMd5,"data-len":e.message.videoDataLen,"compress-type":e.message.videoCompressType,"play-length":e.message.videoPlayLength,"cdn-video-url":e.message.videoCdnUrl,"from-user-name":e.message.videoFromUserName,"video-data":e.message.videoData,"video-path":e.message.videoPath},null,8,["msg-id","wxid","to-wxid","aes-key","md5","new-md5","data-len","compress-type","play-length","cdn-video-url","from-user-name","video-data","video-path"])):E("",!0)])):"emoji"===e.message.type?(A(),j("div",zl,[e.message.emojiUrl||e.message.emojiThumbUrl?(A(),F(il,{key:0,"emoji-url":e.message.emojiUrl,"emoji-thumb-url":e.message.emojiThumbUrl,"emoji-extern-url":e.message.emojiExternUrl,"emoji-aes-key":e.message.emojiAesKey,"emoji-md5":e.message.emojiMd5,"emoji-width":e.message.emojiWidth,"emoji-height":e.message.emojiHeight},null,8,["emoji-url","emoji-thumb-url","emoji-extern-url","emoji-aes-key","emoji-md5","emoji-width","emoji-height"])):(A(),j("div",$l,[N(ae,{class:"emoji-icon"},{default:M(()=>[N(L(ze))]),_:1}),z("span",El,H(e.message.content),1)]))])):E("",!0)],2),"failed"===e.message.status&&e.message.canRetry?(A(),j("div",Hl,[N(le,{type:"danger",size:"small",icon:L($e),circle:"",title:"重新发送",onClick:c},null,8,["icon"])])):E("",!0)]),z("div",Ol,[N(ee,{size:32,src:e.myAvatar},{default:M(()=>[$(H(e.myAvatarText),1)]),_:1},8,["src"])])])):(A(),j("div",dl,[z("div",ml,[N(ee,{src:e.avatar,size:32},{default:M(()=>[$(H(e.avatarText||m().charAt(0)),1)]),_:1},8,["src"])]),z("div",vl,[e.message.isGroupMessage?(A(),j("div",pl,[z("div",fl,H(m()),1)])):E("",!0),z("div",{class:J(["message-content",r.value])},["text"===e.message.type?(A(),j("div",gl,H(e.message.content),1)):"image"===e.message.type?(A(),j("div",yl,[L(t).currentSession?(A(),F(h,{key:0,"msg-id":s.value,wxid:null==(l=L(n).currentAccount)?void 0:l.wxid,"to-wxid":L(t).currentSession.id,"aes-key":e.message.imageAesKey,md5:e.message.imageMd5,"data-len":e.message.imageDataLen,"compress-type":e.message.imageCompressType,"image-data":e.message.imageData,"image-path":e.message.imagePath,"cdn-file-aes-key":e.message.imageCdnFileAesKey,"cdn-file-no":e.message.imageCdnFileNo},null,8,["msg-id","wxid","to-wxid","aes-key","md5","data-len","compress-type","image-data","image-path","cdn-file-aes-key","cdn-file-no"])):E("",!0)])):"video"===e.message.type?(A(),j("div",hl,[L(t).currentSession?(A(),F(x,{key:0,"msg-id":s.value,wxid:null==(i=L(n).currentAccount)?void 0:i.wxid,"to-wxid":L(t).currentSession.id,"aes-key":e.message.videoAesKey,md5:e.message.videoMd5,"new-md5":e.message.videoNewMd5,"data-len":e.message.videoDataLen,"compress-type":e.message.videoCompressType,"play-length":e.message.videoPlayLength,"cdn-video-url":e.message.videoCdnUrl,"from-user-name":e.message.videoFromUserName,"video-data":e.message.videoData,"video-path":e.message.videoPath},null,8,["msg-id","wxid","to-wxid","aes-key","md5","new-md5","data-len","compress-type","play-length","cdn-video-url","from-user-name","video-data","video-path"])):E("",!0)])):"file"===e.message.type?(A(),j("div",xl,[N(w,{"file-name":(null==(p=e.message.fileData)?void 0:p.name)||"未知文件","file-size":(null==(f=e.message.fileData)?void 0:f.size)||0,"file-url":g(e.message.fileData),"mime-type":v(null==(_=e.message.fileData)?void 0:_.name,null==(b=e.message.fileData)?void 0:b.ext),"cdn-url":null==(C=e.message.fileData)?void 0:C.cdnUrl,"aes-key":null==(S=e.message.fileData)?void 0:S.aesKey,"attach-id":null==(V=e.message.fileData)?void 0:V.attachId,wxid:null==(P=L(n).currentAccount)?void 0:P.wxid,"user-name":y(),"app-id":k(),"original-content":null==(D=e.message.fileData)?void 0:D.originalContent,"message-status":e.message.status,"from-me":e.message.fromMe},null,8,["file-name","file-size","file-url","mime-type","cdn-url","aes-key","attach-id","wxid","user-name","app-id","original-content","message-status","from-me"])])):"link"===e.message.type?(A(),j("div",wl,[z("div",{class:"link-card",onClick:a[0]||(a[0]=a=>{var l,t;(t=null==(l=e.message.linkData)?void 0:l.url)&&window.open(t,"_blank")})},[z("div",kl,[z("div",_l,H((null==(U=e.message.linkData)?void 0:U.title)||e.message.content),1),z("div",bl,H(null==(T=e.message.linkData)?void 0:T.url),1)]),(null==(I=e.message.linkData)?void 0:I.thumbUrl)?(A(),j("div",Cl,[z("img",{src:e.message.linkData.thumbUrl,alt:"链接缩略图"},null,8,Sl)])):E("",!0)])])):"emoji"===e.message.type?(A(),j("div",Vl,[e.message.emojiUrl||e.message.emojiThumbUrl?(A(),F(il,{key:0,"emoji-url":e.message.emojiUrl,"emoji-thumb-url":e.message.emojiThumbUrl,"emoji-extern-url":e.message.emojiExternUrl,"emoji-aes-key":e.message.emojiAesKey,"emoji-md5":e.message.emojiMd5,"emoji-width":e.message.emojiWidth,"emoji-height":e.message.emojiHeight},null,8,["emoji-url","emoji-thumb-url","emoji-extern-url","emoji-aes-key","emoji-md5","emoji-width","emoji-height"])):(A(),j("div",Pl,[N(ae,{class:"emoji-icon"},{default:M(()=>[N(L(ze))]),_:1}),z("span",Dl,H(e.message.content),1)]))])):E("",!0)],2),"failed"===e.message.status&&e.message.canRetry?(A(),j("div",Ul,[N(le,{type:"danger",size:"small",icon:L($e),circle:"",title:"重新发送",onClick:c},null,8,["icon"])])):E("",!0)])]))],34)}}}),[["__scopeId","data-v-b1f03d2f"]]);const Ll=new WeakMap,Wl="undefined"!=typeof window&&"undefined"!=typeof document;"undefined"!=typeof WorkerGlobalScope&&(globalThis,WorkerGlobalScope);const Kl=Object.prototype.toString,ql=()=>{};const Rl=e=>e();function Bl(e){return e.endsWith("rem")?16*Number.parseFloat(e):Number.parseFloat(e)}function Ql(e){return Array.isArray(e)?e:[e]}function Jl(...e){if(1!==e.length)return Z(...e);const a=e[0];return"function"==typeof a?ee(ae(()=>({get:a,set:ql}))):U(a)}function Gl(e,a,l={}){const t=l,{eventFilter:n=Rl}=t,s=u(t,["eventFilter"]);return te(e,(i=n,o=a,function(...e){return new Promise((a,l)=>{Promise.resolve(i(()=>o.apply(this,e),{fn:o,thisArg:this,args:e})).then(a).catch(l)})}),s);var i,o}function Yl(e,a,l={}){const t=l,{eventFilter:n,initialState:s="active"}=t,i=u(t,["eventFilter","initialState"]),{eventFilter:c,pause:d,resume:m,isActive:v}=function(e=Rl,a={}){const{initialState:l="active"}=a,t=Jl("active"===l);return{isActive:ee(t),pause:function(){t.value=!1},resume:function(){t.value=!0},eventFilter:(...a)=>{t.value&&e(...a)}}}(n,{initialState:s});return{stop:Gl(e,a,r(o({},i),{eventFilter:c})),pause:d,resume:m,isActive:v}}function Xl(e,a=!0,l){ne()?T(e,l):a?e():le(e)}const Zl=Wl?window:void 0;function et(e){var a;const l=X(e);return null!=(a=null==l?void 0:l.$el)?a:l}function at(...e){const a=[],l=()=>{a.forEach(e=>e()),a.length=0},t=Q(()=>{const a=Ql(X(e[0])).filter(e=>null!=e);return a.every(e=>"string"!=typeof e)?a:void 0}),n=(s=([e,t,n,s])=>{if(l(),!(null==e?void 0:e.length)||!(null==t?void 0:t.length)||!(null==n?void 0:n.length))return;const i=(r=s,"[object Object]"===Kl.call(r)?o({},s):s);var r;a.push(...e.flatMap(e=>t.flatMap(a=>n.map(l=>((e,a,l,t)=>(e.addEventListener(a,l,t),()=>e.removeEventListener(a,l,t)))(e,a,l,i)))))},te(()=>{var a,l;return[null!=(l=null==(a=t.value)?void 0:a.map(e=>et(e)))?l:[Zl].filter(e=>null!=e),Ql(X(t.value?e[1]:e[0])),Ql(L(t.value?e[2]:e[1])),X(t.value?e[3]:e[2])]},s,r(o({},{flush:"post"}),{immediate:!0})));var s;var i;return i=l,oe()&&re(i),()=>{n(),l()}}function lt(e){const a=function(){const e=Y(!1),a=ne();return a&&T(()=>{e.value=!0},a),e}();return Q(()=>(a.value,Boolean(e())))}const tt=Symbol("vueuse-ssr-width");function nt(){const e=se()?((...e)=>{var a;const l=e[0],t=null==(a=ne())?void 0:a.proxy;if(null==t&&!se())throw new Error("injectLocal must be called in setup");return t&&Ll.has(t)&&l in Ll.get(t)?Ll.get(t)[l]:ie(...e)})(tt,null):null;return"number"==typeof e?e:void 0}const st="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},it="__vueuse_ssr_handlers__",ot=rt();function rt(){return it in st||(st[it]=st[it]||{}),st[it]}function ut(e,a){return ot[e]||a}function ct(e){return function(e,a={}){const{window:l=Zl,ssrWidth:t=nt()}=a,n=lt(()=>l&&"matchMedia"in l&&"function"==typeof l.matchMedia),s=Y("number"==typeof t),i=Y(),o=Y(!1);return ue(()=>{if(s.value){s.value=!n.value;const a=X(e).split(",");return void(o.value=a.some(e=>{const a=e.includes("not all"),l=e.match(/\(\s*min-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/),n=e.match(/\(\s*max-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/);let s=Boolean(l||n);return l&&s&&(s=t>=Bl(l[1])),n&&s&&(s=t<=Bl(n[1])),a?!s:s}))}n.value&&(i.value=l.matchMedia(X(e)),o.value=i.value.matches)}),at(i,"change",e=>{o.value=e.matches},{passive:!0}),Q(()=>o.value)}("(prefers-color-scheme: dark)",e)}const dt={boolean:{read:e=>"true"===e,write:e=>String(e)},object:{read:e=>JSON.parse(e),write:e=>JSON.stringify(e)},number:{read:e=>Number.parseFloat(e),write:e=>String(e)},any:{read:e=>e,write:e=>String(e)},string:{read:e=>e,write:e=>String(e)},map:{read:e=>new Map(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e.entries()))},set:{read:e=>new Set(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e))},date:{read:e=>new Date(e),write:e=>e.toISOString()}},mt="vueuse-storage";function vt(e,a,l,t={}){var n;const{flush:s="pre",deep:i=!0,listenToStorageChanges:r=!0,writeDefaults:u=!0,mergeDefaults:c=!1,shallow:d,window:m=Zl,eventFilter:v,onError:p=e=>{},initOnMounted:f}=t,g=(d?Y:U)("function"==typeof a?a():a),y=Q(()=>X(e));if(!l)try{l=ut("getDefaultStorage",()=>{var e;return null==(e=Zl)?void 0:e.localStorage})()}catch(V){p(V)}if(!l)return g;const h=X(a),x=function(e){return null==e?"any":e instanceof Set?"set":e instanceof Map?"map":e instanceof Date?"date":"boolean"==typeof e?"boolean":"string"==typeof e?"string":"object"==typeof e?"object":Number.isNaN(e)?"any":"number"}(h),w=null!=(n=t.serializer)?n:dt[x],{pause:k,resume:_}=Yl(g,()=>function(e){try{const a=l.getItem(y.value);if(null==e)b(a,null),l.removeItem(y.value);else{const t=w.write(e);a!==t&&(l.setItem(y.value,t),b(a,t))}}catch(V){p(V)}}(g.value),{flush:s,deep:i,eventFilter:v});function b(e,a){if(m){const t={key:y.value,oldValue:e,newValue:a,storageArea:l};m.dispatchEvent(l instanceof Storage?new StorageEvent("storage",t):new CustomEvent(mt,{detail:t}))}}function C(e){if(!e||e.storageArea===l)if(e&&null==e.key)g.value=h;else if(!e||e.key===y.value){k();try{(null==e?void 0:e.newValue)!==w.write(g.value)&&(g.value=function(e){const a=e?e.newValue:l.getItem(y.value);if(null==a)return u&&null!=h&&l.setItem(y.value,w.write(h)),h;if(!e&&c){const e=w.read(a);return"function"==typeof c?c(e,h):"object"!==x||Array.isArray(e)?e:o(o({},h),e)}return"string"!=typeof a?a:w.read(a)}(e))}catch(V){p(V)}finally{e?le(_):_()}}}function S(e){C(e.detail)}return te(y,()=>C(),{flush:s}),m&&r&&Xl(()=>{l instanceof Storage?at(m,"storage",C,{passive:!0}):at(m,mt,S),f&&C()}),f||C(),g}!function(e=!1,a={}){const{truthyValue:l=!0,falsyValue:t=!1}=a,n=G(e),s=Y(e);function i(e){if(arguments.length)return s.value=e,s.value;{const e=X(l);return s.value=s.value===e?X(t):e,s.value}}}(function(e={}){const{valueDark:a="dark",valueLight:l=""}=e,t=function(e={}){const{selector:a="html",attribute:l="class",initialValue:t="auto",window:n=Zl,storage:s,storageKey:i="vueuse-color-scheme",listenToStorageChanges:r=!0,storageRef:u,emitAuto:c,disableTransition:d=!0}=e,m=o({auto:"",light:"light",dark:"dark"},e.modes||{}),v=ct({window:n}),p=Q(()=>v.value?"dark":"light"),f=u||(null==i?Jl(t):vt(i,t,s,{window:n,listenToStorageChanges:r})),g=Q(()=>"auto"===f.value?p.value:f.value),y=ut("updateHTMLAttrs",(e,a,l)=>{const t="string"==typeof e?null==n?void 0:n.document.querySelector(e):et(e);if(!t)return;const s=new Set,i=new Set;let o,r=null;if("class"===a){const e=l.split(/\s/g);Object.values(m).flatMap(e=>(e||"").split(/\s/g)).filter(Boolean).forEach(a=>{e.includes(a)?s.add(a):i.add(a)})}else r={key:a,value:l};if(0!==s.size||0!==i.size||null!==r){d&&(o=n.document.createElement("style"),o.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),n.document.head.appendChild(o));for(const e of s)t.classList.add(e);for(const e of i)t.classList.remove(e);r&&t.setAttribute(r.key,r.value),d&&(n.getComputedStyle(o).opacity,document.head.removeChild(o))}});function h(e){var t;y(a,l,null!=(t=m[e])?t:e)}function x(a){e.onChanged?e.onChanged(a,h):h(a)}te(g,x,{flush:"post",immediate:!0}),Xl(()=>x(g.value));const w=Q({get:()=>c?f.value:g.value,set(e){f.value=e}});return Object.assign(w,{store:f,system:p,state:g})}(r(o({},e),{onChanged:(a,l)=>{var t;e.onChanged?null==(t=e.onChanged)||t.call(e,"dark"===a,l,a):l(a)},modes:{dark:a,light:l}})),n=Q(()=>t.system.value);return Q({get:()=>"dark"===t.value,set(e){const a=e?"dark":"light";n.value===a?t.value="auto":t.value=a}})}());const pt={class:"chat-interface"},ft={class:"chat-sessions"},gt={class:"sessions-header"},yt={class:"search-container"},ht={class:"sessions-list"},xt={key:0,class:"empty-sessions"},wt={class:"empty-content"},kt=["onClick","onContextmenu"],_t={class:"session-avatar"},bt={class:"avatar-text"},Ct={class:"session-content"},St={class:"session-top"},Vt={class:"session-name"},Pt={class:"session-time"},Dt={class:"session-bottom"},Ut={class:"session-last-message"},Tt={key:0,class:"unread-badge"},It={class:"chat-area"},jt={key:0,class:"no-session"},At={key:1,class:"chat-content"},Nt={class:"chat-header"},Mt={class:"chat-info"},zt={class:"avatar-text"},$t={class:"chat-title"},Et={class:"chat-name"},Ht={class:"chat-actions"},Ot={key:0,class:"empty-messages"},Ft={class:"empty-messages-content"},Lt={key:1,class:"empty-messages"},Wt={class:"empty-messages-content"},Kt={key:2,class:"messages-list"},qt={class:"input-toolbar"},Rt={class:"input-container"},Bt={class:"input-wrapper"},Qt={class:"input-actions"},Jt={class:"dialog-footer"},Gt=v(D({__name:"ChatInterface",props:{account:{}},setup(e){const a=e,l=f(),t=P(),n=p(),{showError:s,showSuccess:i}=function(e={}){var a,l;const t={duration:e.defaultDuration||3e3,showClose:null==(a=e.showClose)||a,center:null!=(l=e.center)&&l},n={success:(e,a)=>be.success(o(o({message:e},t),a)),warning:(e,a)=>be.warning(o(o({message:e},t),a)),info:(e,a)=>be.info(o(o({message:e},t),a)),error:(e,a)=>be.error(o({message:e,duration:5e3},a))},s={success:(e,a,l)=>Ee.success(o({title:e,message:a,duration:t.duration},l)),warning:(e,a,l)=>Ee.warning(o({title:e,message:a,duration:t.duration},l)),info:(e,a,l)=>Ee.info(o({title:e,message:a,duration:t.duration},l)),error:(e,a,l)=>Ee.error(o({title:e,message:a,duration:0},l))},i=(e,a="确认",l)=>d(null,null,function*(){try{return yield He.confirm(e,a,o({confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"},l)),!0}catch(t){return!1}});return{message:n,notification:s,confirm:i,prompt:(e,a="输入",l)=>d(null,null,function*(){try{const{value:t}=yield He.prompt(e,a,o({confirmButtonText:"确定",cancelButtonText:"取消"},l));return t}catch(t){return null}}),alert:(e,a="提示",l)=>d(null,null,function*(){try{return yield He.alert(e,a,o({confirmButtonText:"确定"},l)),!0}catch(t){return!1}}),showSuccess:e=>n.success(e),showWarning:e=>n.warning(e),showInfo:e=>n.info(e),showError:e=>n.error(e),notifySuccess:(e,a)=>s.success(e,a),notifyWarning:(e,a)=>s.warning(e,a),notifyInfo:(e,a)=>s.info(e,a),notifyError:(e,a)=>s.error(e,a),showApiError:e=>{var a,l;const t=(null==(l=null==(a=null==e?void 0:e.response)?void 0:a.data)?void 0:l.message)||(null==e?void 0:e.message)||"操作失败";n.error(t)},showNetworkError:()=>{n.error("网络连接异常，请检查网络设置")},showOperationSuccess:(e="操作")=>{n.success(`${e}成功`)},confirmDelete:(e="此项")=>i(`确定要删除${e}吗？此操作不可撤销。`,"删除确认",{type:"error"}),confirmLogout:()=>i("确定要退出登录吗？","退出确认",{type:"warning"})}}(),r=U(""),u=U(),c=U(),m=U(),v=U(!1),y=U(""),h=U({visible:!1,x:0,y:0,message:null}),x=U({visible:!1,x:0,y:0,session:null}),w=U(!1),k=U({remark:""}),_=U(null),b=Q(()=>y.value?l.sessions.filter(e=>e.name.toLowerCase().includes(y.value.toLowerCase())):l.sessions);function C(){return d(this,null,function*(){var e;if(null==(e=a.account)?void 0:e.wxid)try{yield t.loadFriends(a.account.wxid);const e=t.currentFriends(a.account.wxid).map(e=>({id:e.wxid,name:e.remark||e.nickname,avatar:e.avatar,type:"friend",lastMessage:"",lastMessageTime:new Date,unreadCount:0,isOnline:e.isOnline}));l.setSessions(e)}catch(n){s("加载好友列表失败")}})}function S(){return d(this,null,function*(){if(!r.value.trim()||!a.account||!l.currentSession)return;const e=r.value.trim();r.value="";try{yield l.sendTextMessage(a.account.wxid,l.currentSession.id,e),we()}catch(t){s("发送消息失败")}})}function V(e){return d(this,null,function*(){var a;const l=null==(a=e.clipboardData)?void 0:a.items;if(l)for(const e of l)if("file"===e.kind){const a=e.getAsFile();a&&(a.type.startsWith("image/")?yield D(a):yield Z(a))}})}function D(e){return d(this,null,function*(){if(a.account&&l.currentSession)try{const t=new FileReader;t.onload=e=>d(null,null,function*(){var t;const n=null==(t=e.target)?void 0:t.result;yield l.sendImageMessage(a.account.wxid,l.currentSession.id,n),we()}),t.readAsDataURL(e)}catch(t){s("发送图片失败")}})}function O(){var e;null==(e=m.value)||e.click()}function G(){var e;null==(e=c.value)||e.click()}function Y(e){var a;const l=e.target,t=null==(a=l.files)?void 0:a[0];t&&D(t),l.value=""}function X(e){var a;const l=e.target,t=null==(a=l.files)?void 0:a[0];t&&(t.type.startsWith("image/")?D(t):Z(t)),l.value=""}function Z(e){return d(this,null,function*(){if(l.currentSession&&n.currentAccount)try{yield l.sendFileMessage(n.currentAccount.wxid,l.currentSession.id,e),i("文件发送成功")}catch(a){s("发送文件失败")}else s("请先选择聊天对象")})}function ee(e){e.preventDefault(),e.stopPropagation()}function ae(e){e.preventDefault(),e.stopPropagation(),v.value=!0}function ne(e){var a;e.preventDefault(),e.stopPropagation(),(null==(a=e.currentTarget)?void 0:a.contains(e.relatedTarget))||(v.value=!1)}function se(e){var a;e.preventDefault(),e.stopPropagation(),v.value=!1;const l=null==(a=e.dataTransfer)?void 0:a.files;if(l&&l.length>0){const e=l[0];e.type.startsWith("image/")?D(e):Z(e)}}function ie(){return d(this,null,function*(){if(l.currentSession)try{yield He.confirm("确定要清空当前会话的所有消息吗？","确认清空",{type:"warning"});const e=l.currentSession.id;l.clearMessages(e),be.success("消息已清空")}catch(e){}})}function oe(e){return d(this,null,function*(){if(a.account&&l.currentSession)try{yield l.retryMessage(a.account.wxid,l.currentSession.id,e.id)}catch(t){s("重试发送失败")}})}function re(e,a){if(e.preventDefault(),!a.fromMe||"system"===a.type||!a.canRecall)return;const l=document.querySelector(".chat-interface");null==l||l.getBoundingClientRect();let t=e.clientX,n=e.clientY;t+120>window.innerWidth&&(t=window.innerWidth-120-10),n+50>window.innerHeight&&(n=window.innerHeight-50-10),h.value={visible:!0,x:t,y:n,message:a}}function ue(){h.value.visible=!1,h.value.message=null}function de(){x.value.visible=!1,x.value.session=null}function me(e){const t=x.value.session;if(t){switch(e){case"remark":_.value=t,k.value.remark=t.name||"",w.value=!0;break;case"deleteSession":!function(e){d(this,null,function*(){try{yield He.confirm(`确定要删除与 "${e.name}" 的聊天记录吗？这不会删除好友关系。`,"删除会话",{type:"warning",confirmButtonText:"删除",cancelButtonText:"取消"}),l.removeSession(e.id),be.success("聊天记录已删除")}catch(a){}})}(t);break;case"deleteFriend":!function(e){d(this,null,function*(){try{if(yield He.confirm(`确定要删除好友 "${e.name}" 吗？这将永久删除好友关系和聊天记录。`,"删除好友",{type:"error",confirmButtonText:"删除",cancelButtonText:"取消",confirmButtonClass:"el-button--danger"}),!a.account)return void be.error("账号信息不存在");yield g.deleteFriend({Wxid:a.account.wxid,ToWxid:e.id}),l.removeSession(e.id),be.success("好友删除成功")}catch(t){"cancel"!==t&&be.error(t.message||"删除好友失败")}})}(t)}de()}}function ve(){return d(this,null,function*(){if(_.value&&a.account)try{const e=_.value,t=k.value.remark.trim();if(!t)return void be.warning("请输入备注名称");if("friend"===e.type)yield g.setFriendRemark({Wxid:a.account.wxid,ToWxid:e.id,Remarks:t});else if("group"===e.type)return void be.warning("群聊备注功能暂未实现");l.updateSessionName(e.id,t),be.success("备注修改成功"),w.value=!1,_.value=null,k.value.remark=""}catch(e){be.error(e.message||"修改备注失败")}})}function ge(){w.value=!1,_.value=null,k.value.remark=""}function ye(){return d(this,null,function*(){if(a.account&&l.currentSession&&h.value.message)try{yield He.confirm("确定要撤回这条消息吗？","撤回消息",{type:"warning"}),yield l.recallMessage(a.account.wxid,l.currentSession.id,h.value.message.id),ue()}catch(e){"cancel"!==e&&s("撤回消息失败")}})}function we(){le(()=>{if(u.value){const e=u.value;e.scrollTo({top:e.scrollHeight,behavior:"smooth"})}})}function ke(e){const a=e instanceof Date?e:new Date(e),l=new Date,t=new Date(l.getFullYear(),l.getMonth(),l.getDate()),n=new Date(t.getTime()-864e5),s=new Date(a.getFullYear(),a.getMonth(),a.getDate());if(s.getTime()===t.getTime())return a.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"});if(s.getTime()===n.getTime())return"昨天";if(l.getTime()-a.getTime()<6048e5){return["周日","周一","周二","周三","周四","周五","周六"][a.getDay()]}return a.toLocaleDateString("zh-CN",{month:"2-digit",day:"2-digit"})}function _e(e,a){if(0===a)return!0;const t=l.currentMessages[a-1];if(!t)return!0;const n=new Date(e.timestamp),s=new Date(t.timestamp),i=60*n.getHours()+n.getMinutes(),o=60*s.getHours()+s.getMinutes();return n.toDateString()!==s.toDateString()||i!==o}function Se(e){return e.fromMe?"":!e.isGroupMessage&&l.currentSession&&l.currentSession.avatar||""}function Ve(e){if(e.fromMe)return"";let a="";return a=e.actualSenderName&&e.actualSenderName.trim()?e.actualSenderName.trim().charAt(0):e.isGroupMessage&&e.actualSender&&e.actualSender.trim()?e.actualSender.trim().charAt(0):l.currentSession&&l.currentSession.name.trim()?l.currentSession.name.trim().charAt(0):e.sessionId&&e.sessionId.trim()?e.sessionId.trim().charAt(0):"?",a||"?"}const Ue=U(!1);function Ie(){return d(this,null,function*(){var e,t,n,o,r,u;if((null==(e=a.account)?void 0:e.wxid)&&l.currentSession&&!Ue.value){Ue.value=!0;try{const e=yield g.getFriendDetail({Wxid:a.account.wxid,Towxids:l.currentSession.id,ChatRoom:l.currentSession.id.includes("@chatroom")?l.currentSession.id:"",force_refresh:!0});if(e.Success&&e.Data){let a=null;if(Array.isArray(e.Data)&&e.Data.length>0?a=e.Data[0]:e.Data.ContactList&&e.Data.ContactList.length>0&&(a=e.Data.ContactList[0]),a){const e=l.currentSession.id.includes("@chatroom");let c=l.currentSession.name,d=l.currentSession.avatar;e?(c=a.ContactList&&a.ContactList.length>0?(null==(t=a.ContactList[0].NickName)?void 0:t.string)||l.currentSession.name:(null==(n=a.NickName)?void 0:n.string)||(null==(o=a.Remark)?void 0:o.string)||l.currentSession.name,d=a.SmallHeadImgUrl||a.BigHeadImgUrl||l.currentSession.avatar):(c=(null==(r=a.Remark)?void 0:r.string)||(null==(u=a.NickName)?void 0:u.string)||a.Alias||l.currentSession.name,d=a.SmallHeadImgUrl||a.BigHeadImgUrl||l.currentSession.avatar);l.updateSessionInfo(l.currentSession.id,{name:c,avatar:d,type:e?"group":"friend"})?i("联系人信息已更新"):s("更新会话信息失败")}else s("未能获取到联系人详情")}else s("未能获取到联系人详情")}catch(c){s("刷新联系人信息失败")}finally{Ue.value=!1}}})}te(()=>{var e;return null==(e=a.account)?void 0:e.wxid},(e,a)=>d(null,null,function*(){if(e&&e!==a){l.switchAccount(e,a),0===l.sessions.length&&(yield C());try{yield l.connectWebSocket(e)}catch(t){}}})),te(()=>l.currentSession,(e,a)=>{e&&e!==a&&le(()=>{we()})});let Me=0;return te(()=>l.currentMessages.length,e=>{e>Me&&Me>0&&we(),Me=e}),T(()=>d(null,null,function*(){var e;if(null==(e=a.account)?void 0:e.wxid){l.loadCachedData(a.account.wxid),0===l.sessions.length&&(yield C());try{yield l.connectWebSocket(a.account.wxid)}catch(t){}}document.addEventListener("click",ue)})),I(()=>{document.removeEventListener("click",ue),document.removeEventListener("click",de)}),(e,t)=>{const n=Ce,s=he,i=xe,o=Te,d=Fe,p=fe,f=pe,g=je;return A(),j("div",pt,[z("div",ft,[z("div",gt,[z("div",yt,[N(s,{modelValue:y.value,"onUpdate:modelValue":t[0]||(t[0]=e=>y.value=e),placeholder:"搜索",size:"small",class:"search-input"},{prefix:M(()=>[N(n,{class:"search-icon"},{default:M(()=>[N(L(Pe))]),_:1})]),_:1},8,["modelValue"])])]),z("div",ht,[0===b.value.length?(A(),j("div",xt,[z("div",wt,[N(n,{class:"empty-icon"},{default:M(()=>[N(L(Oe))]),_:1}),t[10]||(t[10]=z("p",null,"暂无聊天记录",-1)),N(i,{link:"",onClick:C},{default:M(()=>t[9]||(t[9]=[$(" 从好友列表加载 ",-1)])),_:1,__:[9]})])])):E("",!0),(A(!0),j(K,null,q(b.value,e=>{var a;return A(),j("div",{key:e.id,class:J(["session-item",{active:(null==(a=L(l).currentSession)?void 0:a.id)===e.id}]),onClick:a=>function(e){l.setCurrentSession(e.id),le(()=>{we()})}(e),onContextmenu:a=>function(e,a){e.preventDefault(),e.stopPropagation();let l=e.clientX,t=e.clientY;l+150>window.innerWidth&&(l=window.innerWidth-150-10),t+140>window.innerHeight&&(t=window.innerHeight-140-10),x.value={visible:!0,x:l,y:t,session:a}}(a,e)},[z("div",_t,[N(o,{src:e.avatar||"",size:40},{default:M(()=>{var a;return[z("span",bt,H((null==(a=e.name)?void 0:a.charAt(0))||"?"),1)]}),_:2},1032,["src"])]),z("div",Ct,[z("div",St,[z("div",Vt,H(e.name),1),z("div",Pt,H(ke(e.lastMessageTime)),1)]),z("div",Dt,[z("div",Ut,H(e.lastMessage||"暂无消息"),1),e.unreadCount?(A(),j("div",Tt,H(e.unreadCount>99?"99+":e.unreadCount),1)):E("",!0)])])],42,kt)}),128))])]),z("div",It,[L(l).currentSession?(A(),j("div",At,[z("div",Nt,[z("div",Mt,[N(o,{src:L(l).currentSession.avatar,size:32,class:"chat-avatar"},{default:M(()=>[z("span",zt,H(L(l).currentSession.name.charAt(0)),1)]),_:1},8,["src"]),z("div",$t,[z("div",Et,H(L(l).currentSession.name),1)])]),z("div",Ht,[N(i,{link:"",class:"action-btn",loading:Ue.value,onClick:Ie},{default:M(()=>[N(n,null,{default:M(()=>[N(L(Le))]),_:1}),t[12]||(t[12]=$(" 刷新信息 ",-1))]),_:1,__:[12]},8,["loading"]),N(i,{link:"",class:"action-btn",onClick:ie},{default:M(()=>t[13]||(t[13]=[$(" 清空消息 ",-1)])),_:1,__:[13]})])]),z("div",{ref_key:"messagesContainer",ref:u,class:"messages-container"},[L(l).currentSession?0===L(l).currentMessages.length?(A(),j("div",Lt,[z("div",Wt,[N(n,{class:"empty-messages-icon"},{default:M(()=>[N(L(De))]),_:1}),t[16]||(t[16]=z("p",null,"暂无聊天记录",-1)),t[17]||(t[17]=z("span",null,"发送一条消息开始聊天吧",-1))])])):L(l).currentSession&&L(l).currentMessages.length>0?(A(),j("div",Kt,[(A(!0),j(K,null,q(L(l).currentMessages,(e,l)=>{var t,n,s,i;return A(),F(Fl,{key:e.id,message:e,"show-time":_e(e,l),avatar:Se(e),"avatar-text":Ve(e),"my-avatar":(null==(t=a.account)?void 0:t.headUrl)||(null==(n=a.account)?void 0:n.avatar),"my-avatar-text":(null==(i=null==(s=a.account)?void 0:s.nickname)?void 0:i.charAt(0))||"我",onRetry:oe,onContextmenu:re},null,8,["message","show-time","avatar","avatar-text","my-avatar","my-avatar-text"])}),128))])):E("",!0):(A(),j("div",Ot,[z("div",Ft,[N(n,{class:"empty-messages-icon"},{default:M(()=>[N(L(De))]),_:1}),t[14]||(t[14]=z("p",null,"请选择一个会话",-1)),t[15]||(t[15]=z("span",null,"点击左侧会话列表开始聊天",-1))])]))],512),(A(),F(ce,{to:"body"},[h.value.visible?(A(),j("div",{key:0,class:"context-menu",style:B({left:`${h.value.x}px`,top:`${h.value.y}px`}),onClick:t[1]||(t[1]=R(()=>{},["stop"]))},[z("div",{class:"context-menu-item",onClick:ye},[N(n,null,{default:M(()=>[N(L(Ne))]),_:1}),t[18]||(t[18]=$(" 撤回消息 ",-1))])],4)):E("",!0)])),z("div",{class:J(["input-area",{"drag-over":v.value}]),onDrop:se,onDragover:ee,onDragenter:ae,onDragleave:ne},[z("div",qt,[N(i,{link:"",class:"toolbar-btn",onClick:O},{default:M(()=>[N(n,null,{default:M(()=>[N(L(ze))]),_:1}),t[19]||(t[19]=$(" 图片 ",-1))]),_:1,__:[19]}),N(i,{link:"",class:"toolbar-btn",onClick:G},{default:M(()=>[N(n,null,{default:M(()=>[N(L(We))]),_:1}),t[20]||(t[20]=$(" 文件 ",-1))]),_:1,__:[20]})]),z("div",Rt,[z("div",Bt,[N(s,{modelValue:r.value,"onUpdate:modelValue":t[2]||(t[2]=e=>r.value=e),type:"textarea",rows:3,placeholder:"输入消息内容，支持粘贴图片...",class:"message-input",onKeydown:W(R(S,["ctrl"]),["enter"]),onPaste:V},null,8,["modelValue","onKeydown"])]),z("div",Qt,[t[22]||(t[22]=z("span",{class:"input-tip"},"Ctrl+Enter 发送",-1)),N(i,{type:"primary",loading:L(l).isSending,class:"send-btn",onClick:S},{default:M(()=>t[21]||(t[21]=[$(" 发送 ",-1)])),_:1,__:[21]},8,["loading"])])]),z("input",{ref_key:"imageInputRef",ref:m,type:"file",accept:"image/*",style:{display:"none"},onChange:Y},null,544),z("input",{ref_key:"fileInputRef",ref:c,type:"file",style:{display:"none"},onChange:X},null,544)],34)])):(A(),j("div",jt,[N(d,{icon:"info",title:"请选择一个聊天会话"},{"sub-title":M(()=>t[11]||(t[11]=[z("p",null,"从左侧选择一个会话开始聊天，或者从好友列表发起新的聊天",-1)])),_:1})]))]),(A(),F(ce,{to:"body"},[x.value.visible?(A(),j("div",{key:0,class:"session-context-menu",style:B({left:x.value.x+"px",top:x.value.y+"px"}),onClick:t[6]||(t[6]=R(()=>{},["stop"]))},[z("div",{class:"context-menu-item",onClick:t[3]||(t[3]=e=>me("remark"))},[N(n,null,{default:M(()=>[N(L(Ae))]),_:1}),t[23]||(t[23]=z("span",null,"修改备注",-1))]),t[26]||(t[26]=z("div",{class:"context-menu-divider"},null,-1)),z("div",{class:"context-menu-item",onClick:t[4]||(t[4]=e=>me("deleteSession"))},[N(n,null,{default:M(()=>[N(L(De))]),_:1}),t[24]||(t[24]=z("span",null,"删除会话",-1))]),z("div",{class:"context-menu-item danger",onClick:t[5]||(t[5]=e=>me("deleteFriend"))},[N(n,null,{default:M(()=>[N(L(Ne))]),_:1}),t[25]||(t[25]=z("span",null,"删除好友",-1))])],4)):E("",!0),x.value.visible?(A(),j("div",{key:1,class:"context-menu-overlay",onClick:de,onContextmenu:R(de,["prevent"])},null,32)):E("",!0)])),N(g,{modelValue:w.value,"onUpdate:modelValue":t[8]||(t[8]=e=>w.value=e),title:"修改备注",width:"400px","before-close":ge},{footer:M(()=>[z("div",Jt,[N(i,{onClick:ge},{default:M(()=>t[27]||(t[27]=[$("取消",-1)])),_:1,__:[27]}),N(i,{type:"primary",onClick:ve},{default:M(()=>t[28]||(t[28]=[$("确定",-1)])),_:1,__:[28]})])]),default:M(()=>[N(f,{model:k.value,"label-width":"80px"},{default:M(()=>[N(p,{label:"备注名称"},{default:M(()=>[N(s,{modelValue:k.value.remark,"onUpdate:modelValue":t[7]||(t[7]=e=>k.value.remark=e),placeholder:"请输入备注名称",maxlength:"50","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-56023a41"]]),Yt={class:"account-manager"},Xt={class:"account-info"},Zt={class:"nickname"},en={class:"wxid"},an=v(D({__name:"AccountManager",emits:["close"],setup(e,{emit:a}){const l=p();return(e,a)=>{const t=qe,n=Ie,s=xe,i=Ke;return A(),j("div",Yt,[N(i,{data:L(l).accounts,style:{width:"100%"}},{default:M(()=>[N(t,{label:"账号信息",width:"250"},{default:M(e=>[z("div",Xt,[z("span",Zt,H(e.row.nickname),1),z("span",en,"["+H(e.row.wxid)+"]",1)])]),_:1}),N(t,{prop:"status",label:"状态",width:"80"},{default:M(e=>[N(n,{type:"online"===e.row.status?"success":"danger"},{default:M(()=>[$(H("online"===e.row.status?"在线":"离线"),1)]),_:2},1032,["type"])]),_:1}),N(t,{prop:"loginTime",label:"登录时间",width:"150"},{default:M(e=>{return[$(H((a=e.row.loginTime,a.toLocaleString())),1)];var a}),_:1}),N(t,{label:"操作",width:"150"},{default:M(e=>["offline"===e.row.status?(A(),F(s,{key:0,type:"primary",size:"small",onClick:a=>function(e){return d(this,null,function*(){try{be.info(`正在重连账号 ${e.nickname}...`),yield new Promise(e=>setTimeout(e,2e3)),e.status="online",e.loginTime=new Date,be.success("重连成功")}catch(a){be.error("重连失败")}})}(e.row)},{default:M(()=>a[0]||(a[0]=[$(" 重连 ",-1)])),_:2,__:[0]},1032,["onClick"])):(A(),F(s,{key:1,type:"warning",size:"small",onClick:a=>function(e){return d(this,null,function*(){try{yield He.confirm(`确定要断开账号 ${e.nickname} 吗？`,"确认断开",{type:"warning"}),l.updateAccountStatus(e.wxid,"offline"),be.success("账号已断开")}catch(a){}})}(e.row)},{default:M(()=>a[1]||(a[1]=[$(" 断开 ",-1)])),_:2,__:[1]},1032,["onClick"])),N(s,{type:"danger",size:"small",onClick:a=>function(e){return d(this,null,function*(){try{yield He.confirm(`确定要删除账号 ${e.nickname} 吗？此操作不可恢复！`,"确认删除",{type:"error"}),l.removeAccount(e.wxid),be.success("账号已删除")}catch(a){}})}(e.row)},{default:M(()=>a[2]||(a[2]=[$(" 删除 ",-1)])),_:2,__:[2]},1032,["onClick"])]),_:1})]),_:1},8,["data"])])}}}),[["__scopeId","data-v-b6ebfd0b"]]),ln=class e{constructor(){c(this,"timers",new Map)}static getInstance(){return e.instance||(e.instance=new e),e.instance}createTimer(e,a,l,t="timeout"){this.clearTimer(e);const n="interval"===t?setInterval(a,l):setTimeout(a,l);return this.timers.set(e,n),n}clearTimer(e){const a=this.timers.get(e);a&&(clearTimeout(a),clearInterval(a),this.timers.delete(e))}clearAllTimers(){this.timers.forEach((e,a)=>{clearTimeout(e),clearInterval(e)}),this.timers.clear()}hasTimer(e){return this.timers.has(e)}getTimerKeys(){return Array.from(this.timers.keys())}getTimerCount(){return this.timers.size}};c(ln,"instance",null);const tn=ln.getInstance();"undefined"!=typeof window&&window.addEventListener("beforeunload",()=>{tn.clearAllTimers()});const nn=class e{constructor(){c(this,"activeCheckers",new Map)}static getInstance(){return e.instance||(e.instance=new e),e.instance}startCheck(e,a){this.stopCheck(e);const l=a.interval||2e3,t=a.timeout||3e5,n=setInterval(()=>d(this,null,function*(){try{yield this.checkStatus(e,a)}catch(l){a.onError&&a.onError(l instanceof Error?l.message:"检查失败")}}),l),s=setTimeout(()=>{this.stopCheck(e),a.onExpired&&a.onExpired(),a.onStatusChange&&a.onStatusChange("二维码已过期，请刷新")},t);this.activeCheckers.set(e,{config:a,intervalTimer:n,timeoutTimer:s})}stopCheck(e){const a=this.activeCheckers.get(e);a&&(a.intervalTimer&&clearInterval(a.intervalTimer),a.timeoutTimer&&clearTimeout(a.timeoutTimer),this.activeCheckers.delete(e))}stopAllChecks(){this.activeCheckers.forEach((e,a)=>{this.stopCheck(a)})}checkStatus(e,a){return d(this,null,function*(){const l=yield m.checkQRCodeStatus({Uuid:a.uuid});if(l.Success&&"登录成功"===l.Message)this.stopCheck(e),a.onSuccess&&a.onSuccess(l.Data),a.onStatusChange&&a.onStatusChange("登录成功！正在初始化...",l.Data);else if(l.Success&&l.Data){const t=l.Data;t.expiredTime<=0?(this.stopCheck(e),a.onExpired&&a.onExpired(),a.onStatusChange&&a.onStatusChange("二维码已过期，请刷新")):0===t.status?a.onStatusChange&&a.onStatusChange(`等待扫码... (${t.expiredTime}秒后过期)`,t):1===t.status?a.onStatusChange&&a.onStatusChange(`${t.nickName||"用户"}已扫码，请在手机上确认登录 (${t.expiredTime}秒后过期)`,t):4===t.status?(this.stopCheck(e),a.onCancelled&&a.onCancelled(),a.onStatusChange&&a.onStatusChange("用户取消登录")):a.onStatusChange&&a.onStatusChange(`状态: ${t.status} (${t.expiredTime}秒后过期)`,t)}else a.onError&&a.onError(l.Message||"未知错误"),a.onStatusChange&&a.onStatusChange(`检测失败: ${l.Message||"未知错误"}`)})}getActiveCount(){return this.activeCheckers.size}getActiveKeys(){return Array.from(this.activeCheckers.keys())}hasChecker(e){return this.activeCheckers.has(e)}};c(nn,"instance",null);const sn=nn.getInstance();"undefined"!=typeof window&&window.addEventListener("beforeunload",()=>{sn.stopAllChecks()});const on={key:0,class:"account-management"},rn={class:"card-header"},un={class:"account-details"},cn={class:"avatar-section"},dn={class:"info-section"},mn={class:"info-grid"},vn={class:"info-item"},pn={class:"info-item"},fn={class:"info-item"},gn={class:"info-item"},yn={class:"info-item"},hn={class:"info-item"},xn={class:"info-item"},wn={class:"info-item"},kn={class:"info-item"},_n={class:"info-item"},bn={class:"info-item"},Cn={class:"info-item"},Sn={class:"card-header"},Vn={class:"header-left"},Pn={class:"header-right"},Dn={key:0,class:"proxy-form"},Un={class:"proxy-mode-selection"},Tn={key:0,class:"proxy-preset-section"},In={class:"proxy-preset-actions"},jn={class:"proxy-presets"},An={class:"preset-buttons"},Nn={class:"proxy-input-group"},Mn={class:"proxy-type"},zn={class:"proxy-address"},$n={class:"proxy-port"},En={class:"proxy-auth"},Hn={class:"proxy-actions"},On={key:1,class:"proxy-disabled"},Fn={class:"auto-accept-section"},Ln={class:"setting-item"},Wn={class:"setting-label"},Kn={class:"action-buttons"},qn={key:0,class:"qr-login"},Rn={class:"qr-container"},Bn=["src"],Qn={class:"qr-status"},Jn={key:1,class:"qr-loading"},Gn=v(D({__name:"AccountManagementModal",props:{modelValue:{type:Boolean},account:{}},emits:["update:modelValue","account-updated"],setup(e,{emit:a}){const l=e,t=a,n=p(),{startCheck:s,stopCheck:i,stopAllChecks:u}={startCheck:(e,a)=>{sn.startCheck(e,a)},stopCheck:e=>{sn.stopCheck(e)},stopAllChecks:()=>{sn.stopAllChecks()},hasChecker:e=>sn.hasChecker(e),getActiveCount:()=>sn.getActiveCount(),getActiveKeys:()=>sn.getActiveKeys()},c=Q({get:()=>l.modelValue,set:e=>t("update:modelValue",e)}),v=U(!1),f=U(!1),y=U(!1),h=U(!1),x=U(!1),w=U(!1),_=U(!1),b=U(""),P=U(!1),D=U("等待获取二维码"),O=U(null),W=U(null),R=U(""),B=U(!1),G=U(!1),Y=U(""),X=U(!1),Z=U("manual"),ee=U([]),ae=U([]),le=U([]),ne=U(""),se=U(null),ie=U(null),oe=U(!1),re=[{name:"V2Ray",Type:"SOCKS5",Host:"127.0.0.1",Port:1080},{name:"Clash",Type:"SOCKS5",Host:"127.0.0.1",Port:7890},{name:"SSR",Type:"SOCKS5",Host:"127.0.0.1",Port:1086},{name:"HTTP代理",Type:"HTTP",Host:"127.0.0.1",Port:8080}],ue=()=>d(null,null,function*(){try{const e=yield C.getAvailableProxies();if(0===e.code){ee.value=e.data.list,ae.value=e.data.list;const a=new Set;e.data.list.forEach(e=>{e.country&&a.add(e.country)}),le.value=Array.from(a).sort()}}catch(e){}}),ce=()=>{ne.value?ae.value=ee.value.filter(e=>e.country===ne.value):ae.value=ee.value,se.value=null,ie.value=null},de=()=>{se.value&&(ie.value=ee.value.find(e=>e.id===se.value)||null,ie.value&&me(ie.value))},me=e=>{we.value={Type:"SOCKS5",Host:e.ip,Port:e.port,ProxyIp:e.ip,ProxyUser:e.username,ProxyPassword:e.password}},ve=()=>{ue()},pe=()=>{"manual"===Z.value?(se.value=null,ie.value=null):"preset"===Z.value&&(we.value={Type:"SOCKS5",Host:"",Port:1080,ProxyIp:"",ProxyUser:"",ProxyPassword:""})},we=U({Type:"SOCKS5",Host:"",Port:1080,ProxyIp:"",ProxyUser:"",ProxyPassword:""});te(()=>l.account,e=>d(null,null,function*(){e&&(yield ke(e.wxid))}),{immediate:!0});const ke=e=>d(null,null,function*(){var a,t,n,s;try{const t=yield m.getProxy({Wxid:e});if(t.Success&&t.Data)if(v.value=!0,we.value=o({},t.Data),Y.value="inactive",t.Data.ProxyIp&&t.Data.ProxyIp!==t.Data.Host){Z.value="preset";const e=ee.value.find(e=>e.ip===t.Data.ProxyIp);e&&(ie.value=e,se.value=e.id)}else Z.value="manual";else(null==(a=l.account)?void 0:a.proxy)?(v.value=!0,we.value=o({},l.account.proxy),Y.value="inactive",Z.value="manual"):(v.value=!1,Y.value="",Z.value="manual",we.value={Type:"SOCKS5",Host:"",Port:1080,ProxyIp:"",ProxyUser:"",ProxyPassword:""})}catch(i){(null==(t=i.message)?void 0:t.includes("404"))||null==(n=i.message)||n.includes("不存在"),(null==(s=l.account)?void 0:s.proxy)?(v.value=!0,we.value=o({},l.account.proxy),Y.value="inactive",Z.value="manual"):(v.value=!1,Y.value="",Z.value="manual",we.value={Type:"SOCKS5",Host:"",Port:1080,ProxyIp:"",ProxyUser:"",ProxyPassword:""})}});te(()=>[we.value.Host,we.value.Port,we.value.Type],()=>{v.value&&"testing"!==Y.value&&(Y.value="inactive")});const Se=e=>{if(!e)return"无";return("string"==typeof e?new Date(e):e).toLocaleString("zh-CN")},Ve=()=>{Ke(),_.value=!1,c.value=!1},Pe=e=>{e||Ae()},De=()=>d(null,null,function*(){if(l.account){f.value=!0;try{const e=yield m.setProxy({Wxid:l.account.wxid,Proxy:we.value});if(!e.Success)throw new Error(e.Message||"保存代理设置失败");be.success("代理设置保存成功"),l.account&&(l.account.proxy=o({},we.value),t("account-updated",l.account))}catch(e){be.error(e.message||"保存代理设置失败")}finally{f.value=!1}}}),Ae=()=>d(null,null,function*(){if(l.account){f.value=!0;try{const e=yield m.setProxy({Wxid:l.account.wxid,Proxy:{}});if(!e.Success)throw new Error(e.Message||"清除代理设置失败");be.success("代理设置已清除"),v.value=!1,Y.value="",we.value={Type:"SOCKS5",Host:"",Port:1080,ProxyIp:"",ProxyUser:"",ProxyPassword:""},l.account&&(l.account.proxy=void 0,t("account-updated",l.account))}catch(e){be.error(e.message||"清除代理设置失败")}finally{f.value=!1}}}),Me=()=>d(null,null,function*(){var e,a;if(l.account&&we.value.Host&&we.value.Port){X.value=!0,Y.value="testing";try{const e=yield m.testProxy({Type:we.value.Type,Host:we.value.Host,Port:we.value.Port,ProxyUser:we.value.ProxyUser||"",ProxyPassword:we.value.ProxyPassword||""});if(e.Success&&e.Data)if("success"===e.Data.status){Y.value="active";const a=e.Data.responseTime?` (${e.Data.responseTime}ms)`:"";be.success(`代理连接测试成功${a}`)}else Y.value="error",be.error(`代理连接测试失败: ${e.Data.error||"未知错误"}`);else Y.value="error",be.error(e.Message||"代理连接测试失败")}catch(t){Y.value="error",(null==(e=t.message)?void 0:e.includes("404"))||(null==(a=t.message)?void 0:a.includes("不存在"))?be.warning("代理测试接口暂未实现，请手动验证代理配置"):be.error("代理连接测试失败")}finally{X.value=!1}}else be.warning("请先配置代理信息")}),ze=e=>({active:"连接正常",testing:"测试中",error:"连接失败",inactive:"未测试"}[e]||"未知"),$e=()=>d(null,null,function*(){if(l.account){Ke(),P.value=!0,D.value="正在获取二维码...";try{const e=yield m.getQRCodeForDeviceReuse({DeviceID:l.account.deviceId||l.account.imei||"",DeviceName:l.account.deviceName,Proxy:l.account.proxy});if(1!==e.Code&&!0!==e.Success||!e.Data)throw new Error(e.Message||"生成二维码失败");if(e.Data.QrBase64)b.value=e.Data.QrBase64,R.value=e.Data.Uuid||e.Data.uuid||"",D.value="请使用微信扫描二维码",R.value&&Oe();else{if(!e.Data.QrUrl)throw new Error("响应中没有找到二维码数据");b.value=e.Data.QrUrl,R.value=e.Data.Uuid||e.Data.uuid||"",D.value="请使用微信扫描二维码",R.value&&Oe()}}catch(e){be.error(e.message||"生成二维码失败"),D.value="生成二维码失败"}finally{P.value=!1}}}),Ee=()=>{$e()},Oe=()=>{var e;if(!R.value)return;const a=`account-modal-${(null==(e=l.account)?void 0:e.wxid)||"unknown"}`;s(a,{uuid:R.value,onSuccess:e=>d(null,null,function*(){var a;if(D.value="登录成功！正在初始化...",(null==(a=l.account)?void 0:a.proxy)&&l.account.proxy.Host)try{const a=yield m.setProxy({Wxid:e.wxid,Type:l.account.proxy.Type||"SOCKS5",Host:l.account.proxy.Host,Port:l.account.proxy.Port,User:l.account.proxy.ProxyUser||"",Password:l.account.proxy.ProxyPassword||""});a.Success?be.success("扫码登录成功，代理已自动配置"):be.warning(`扫码登录成功，但代理设置失败: ${a.Message}`)}catch(n){be.warning("扫码登录成功，但代理设置失败")}else be.success("扫码登录成功！");e&&e.wxid&&(yield Fe(e.wxid)),setTimeout(()=>{_.value=!1,t("refresh")},2e3)}),onStatusChange:e=>{D.value=e},onError:e=>{D.value=`检测失败: ${e}`},onExpired:()=>{D.value="二维码已过期，请刷新"},onCancelled:()=>{D.value="用户取消登录"}})},Fe=e=>d(null,null,function*(){try{const a=yield fetch("http://localhost:8059/api/Login/LoginTwiceAutoAuth",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:`wxid=${encodeURIComponent(e)}`}),l=yield a.json();l.Success?D.value="二次登录成功！":D.value=`二次登录失败: ${l.Message}`}catch(a){D.value=`二次登录失败: ${a.message}`}}),We=()=>{const e=D.value;return e.includes("成功")||e.includes("已扫码")?"status-success":e.includes("失败")||e.includes("过期")||e.includes("取消")||e.includes("错误")?"status-error":e.includes("等待")||e.includes("扫码")?"status-warning":"status-info"},Ke=()=>{var e;const a=`account-modal-${(null==(e=l.account)?void 0:e.wxid)||"unknown"}`;i(a),O.value&&(clearInterval(O.value),O.value=null),W.value&&(clearTimeout(W.value),W.value=null)},qe=()=>d(null,null,function*(){if(l.account)try{yield He.confirm(`确定要为账号 ${l.account.nickname} 开启心跳吗？`,"确认开启心跳",{type:"info"}),w.value=!0;const e=yield fetch(`http://localhost:8059/api/Login/AutoHeartBeat?wxid=${l.account.wxid}`,{method:"POST",headers:{"Content-Type":"application/json"}});if(!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);{const a=yield e.text();be.success(`心跳开启成功: ${a}`)}}catch(e){"cancel"!==e.message&&be.error(`开启心跳失败: ${e.message}`)}finally{w.value=!1}}),ta=()=>d(null,null,function*(){if(l.account)try{yield He.confirm(`确定要断开账号 ${l.account.nickname} 的连接吗？`,"确认断开",{type:"warning"}),h.value=!0,n.updateAccountStatus(l.account.wxid,"offline"),be.success("账号已断开连接"),t("account-updated",r(o({},l.account),{status:"offline"}))}catch(e){}finally{h.value=!1}}),na=()=>d(null,null,function*(){if(l.account)try{yield He.confirm(`确定要删除账号 ${l.account.nickname} 吗？此操作不可恢复！`,"确认删除",{type:"error"}),x.value=!0,n.removeAccount(l.account.wxid),be.success("账号已删除"),c.value=!1}catch(e){}finally{x.value=!1}});te(_,e=>{e?(b.value="",D.value="等待获取二维码",$e()):(Ke(),b.value="",R.value="",D.value="等待获取二维码")}),te(c,e=>{e||(Ke(),_.value=!1)});const sa=()=>d(null,null,function*(){var e;if(null==(e=l.account)?void 0:e.wxid)try{const e=yield g.getAutoAcceptFriendStatus({Wxid:l.account.wxid});e.Success&&e.Data&&(B.value=e.Data.enable||!1)}catch(a){}}),ia=e=>d(null,null,function*(){var a;if(null==(a=l.account)?void 0:a.wxid){G.value=!0;try{const a=yield g.setAutoAcceptFriendStatus({Wxid:l.account.wxid,Enable:e});a.Success?be.success(e?"已开启自动同意好友":"已关闭自动同意好友"):(B.value=!e,be.error(a.Message||"设置失败"))}catch(t){B.value=!e,be.error(t.message||"设置失败")}finally{G.value=!1}}});return te(()=>l.account,e=>{e&&c.value&&sa()},{immediate:!0}),te(c,e=>{e&&l.account&&sa()}),T(()=>{ue()}),I(()=>{Ke(),u()}),(e,a)=>{var l;const t=Ie,n=Te,s=Ue,i=Ce,o=xe,r=Ye,u=Be,d=Re,m=ye,p=ge,g=fe,C=Qe,U=he,T=_e,I=Xe,O=la,W=je;return A(),j(K,null,[N(k,{modelValue:c.value,"onUpdate:modelValue":a[12]||(a[12]=e=>c.value=e),title:`管理账号 - ${(null==(l=e.account)?void 0:l.nickname)||""}`,width:"90%",top:"5vh","custom-class":"account-management-modal responsive-modal",onClose:Ve},{default:M(()=>[e.account?(A(),j("div",on,[N(s,{class:"account-info-card",shadow:"never"},{header:M(()=>[z("div",rn,[a[16]||(a[16]=z("span",null,"账号信息",-1)),N(t,{type:"online"===e.account.status?"primary":"danger",effect:"light"},{default:M(()=>[$(H("online"===e.account.status?"在线":"离线"),1)]),_:1},8,["type"])])]),default:M(()=>[z("div",un,[z("div",cn,[N(n,{src:e.account.headUrl||e.account.avatar,size:80},{default:M(()=>[$(H(e.account.nickname.charAt(0)),1)]),_:1},8,["src"])]),z("div",dn,[z("div",mn,[z("div",vn,[a[17]||(a[17]=z("label",null,"昵称：",-1)),z("span",null,H(e.account.nickname),1)]),z("div",pn,[a[18]||(a[18]=z("label",null,"微信号：",-1)),z("span",null,H(e.account.wxid),1)]),z("div",fn,[a[19]||(a[19]=z("label",null,"别名：",-1)),z("span",null,H(e.account.alias||"无"),1)]),z("div",gn,[a[20]||(a[20]=z("label",null,"UIN：",-1)),z("span",null,H(e.account.uin||"无"),1)]),z("div",yn,[a[21]||(a[21]=z("label",null,"邮箱：",-1)),z("span",null,H(e.account.email||"无"),1)]),z("div",hn,[a[22]||(a[22]=z("label",null,"手机：",-1)),z("span",null,H(e.account.mobile||"无"),1)]),z("div",xn,[a[23]||(a[23]=z("label",null,"设备类型：",-1)),z("span",null,H(e.account.deviceType),1)]),z("div",wn,[a[24]||(a[24]=z("label",null,"设备名称：",-1)),z("span",null,H(e.account.deviceName),1)]),z("div",kn,[a[25]||(a[25]=z("label",null,"设备ID：",-1)),z("span",null,H(e.account.deviceId||e.account.imei||"无"),1)]),z("div",_n,[a[26]||(a[26]=z("label",null,"登录时间：",-1)),z("span",null,H(Se(e.account.loginTime)),1)]),z("div",bn,[a[27]||(a[27]=z("label",null,"Token刷新：",-1)),z("span",null,H(Se(e.account.refreshTokenDate)),1)]),z("div",Cn,[a[28]||(a[28]=z("label",null,"系统版本：",-1)),z("span",null,H(e.account.osVersion||"无"),1)])])])])]),_:1}),N(s,{class:"proxy-card",shadow:"never"},{header:M(()=>{return[z("div",Sn,[z("div",Vn,[a[29]||(a[29]=z("span",null,"代理设置",-1)),v.value&&Y.value?(A(),F(t,{key:0,type:(e=Y.value,{active:"success",testing:"warning",error:"danger",inactive:"info"}[e]||"info"),size:"small",class:"status-tag"},{default:M(()=>[$(H(ze(Y.value)),1)]),_:1},8,["type"])):E("",!0)]),z("div",Pn,[v.value?(A(),F(o,{key:0,onClick:Me,loading:X.value,size:"small",type:"primary",plain:""},{default:M(()=>[N(i,null,{default:M(()=>[N(L(Ge))]),_:1}),a[30]||(a[30]=$(" 测试连接 ",-1))]),_:1,__:[30]},8,["loading"])):E("",!0),N(r,{modelValue:v.value,"onUpdate:modelValue":a[0]||(a[0]=e=>v.value=e),onChange:Pe,loading:f.value},null,8,["modelValue","loading"])])])];var e}),default:M(()=>[v.value?(A(),j("div",Dn,[z("div",Un,[a[33]||(a[33]=z("div",{class:"mode-label"},"代理配置方式",-1)),N(d,{modelValue:Z.value,"onUpdate:modelValue":a[1]||(a[1]=e=>Z.value=e),onChange:pe,class:"mode-radios"},{default:M(()=>[N(u,{value:"preset"},{default:M(()=>a[31]||(a[31]=[$("选择已有代理",-1)])),_:1,__:[31]}),N(u,{value:"manual"},{default:M(()=>a[32]||(a[32]=[$("手动配置",-1)])),_:1,__:[32]})]),_:1},8,["modelValue"])]),"preset"===Z.value?(A(),j("div",Tn,[N(g,{label:"地区筛选"},{default:M(()=>[N(p,{modelValue:ne.value,"onUpdate:modelValue":a[2]||(a[2]=e=>ne.value=e),placeholder:"选择地区",clearable:"",onChange:ce,size:"large","popper-options":{strategy:"fixed"}},{default:M(()=>[N(m,{label:"全部地区",value:""}),(A(!0),j(K,null,q(le.value,e=>(A(),F(m,{key:e,label:e,value:e},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),N(g,{label:"选择代理"},{default:M(()=>[N(p,{modelValue:se.value,"onUpdate:modelValue":a[3]||(a[3]=e=>se.value=e),placeholder:"选择一个可用的代理",filterable:"",onChange:de,size:"large",style:{width:"100%"},"popper-options":{strategy:"fixed"}},{default:M(()=>[(A(!0),j(K,null,q(ae.value,e=>(A(),F(m,{key:e.id,label:L(V)(e),value:e.id,disabled:"active"!==e.status},null,8,["label","value","disabled"]))),128))]),_:1},8,["modelValue"])]),_:1}),ie.value?(A(),F(g,{key:0},{default:M(()=>[N(C,{title:`已选择代理: ${ie.value.ip}:${ie.value.port} [${ie.value.country||"未知地区"}]`,type:"success",closable:!1,"show-icon":""},null,8,["title"])]),_:1})):E("",!0),z("div",In,[N(o,{size:"small",onClick:ve},{default:M(()=>[N(i,null,{default:M(()=>[N(L(Le))]),_:1}),a[34]||(a[34]=$(" 刷新列表 ",-1))]),_:1,__:[34]}),N(o,{size:"small",type:"primary",onClick:a[4]||(a[4]=e=>oe.value=!0)},{default:M(()=>[N(i,null,{default:M(()=>[N(L(Je))]),_:1}),a[35]||(a[35]=$(" 管理代理 ",-1))]),_:1,__:[35]})])])):E("",!0),"manual"===Z.value?(A(),j(K,{key:1},[z("div",jn,[a[36]||(a[36]=z("div",{class:"preset-label"},"快速设置",-1)),z("div",An,[(A(),j(K,null,q(re,e=>N(o,{key:e.name,size:"small",onClick:a=>(e=>{we.value.Type=e.Type,we.value.Host=e.Host,we.value.Port=e.Port,we.value.ProxyUser="",we.value.ProxyPassword=""})(e),plain:""},{default:M(()=>[$(H(e.name),1)]),_:2},1032,["onClick"])),64))])]),z("div",Nn,[z("div",Mn,[N(p,{modelValue:we.value.Type,"onUpdate:modelValue":a[5]||(a[5]=e=>we.value.Type=e),placeholder:"代理类型",size:"large"},{default:M(()=>[N(m,{label:"SOCKS5",value:"SOCKS5"}),N(m,{label:"HTTP",value:"HTTP"}),N(m,{label:"HTTPS",value:"HTTPS"})]),_:1},8,["modelValue"])]),z("div",zn,[N(U,{modelValue:we.value.Host,"onUpdate:modelValue":a[6]||(a[6]=e=>we.value.Host=e),placeholder:"如: 127.0.0.1",size:"large"},null,8,["modelValue"])]),z("div",$n,[N(T,{modelValue:we.value.Port,"onUpdate:modelValue":a[7]||(a[7]=e=>we.value.Port=e),min:1,max:65535,placeholder:"端口",size:"large","controls-position":"right"},null,8,["modelValue"])])]),z("div",En,[N(U,{modelValue:we.value.ProxyUser,"onUpdate:modelValue":a[8]||(a[8]=e=>we.value.ProxyUser=e),placeholder:"用户名 (可选)",size:"large"},null,8,["modelValue"]),N(U,{modelValue:we.value.ProxyPassword,"onUpdate:modelValue":a[9]||(a[9]=e=>we.value.ProxyPassword=e),type:"password",placeholder:"密码 (可选)",size:"large","show-password":""},null,8,["modelValue"])])],64)):E("",!0),z("div",Hn,[N(o,{type:"primary",onClick:De,loading:f.value},{default:M(()=>a[37]||(a[37]=[$(" 保存代理设置 ",-1)])),_:1,__:[37]},8,["loading"]),N(o,{onClick:Ae,loading:f.value,plain:""},{default:M(()=>a[38]||(a[38]=[$(" 清除代理 ",-1)])),_:1,__:[38]},8,["loading"])])])):(A(),j("div",On,a[39]||(a[39]=[z("div",{class:"disabled-text"},"代理功能已关闭",-1)])))]),_:1}),N(s,{class:"actions-card",shadow:"never"},{header:M(()=>a[40]||(a[40]=[z("span",null,"账号操作",-1)])),default:M(()=>[z("div",Fn,[z("div",Ln,[z("div",Wn,[a[41]||(a[41]=z("span",null,"自动同意好友请求",-1)),N(I,{content:"开启后将自动同意所有好友请求",placement:"top"},{default:M(()=>[N(i,{class:"info-icon"},{default:M(()=>[N(L(Ze))]),_:1})]),_:1})]),N(r,{modelValue:B.value,"onUpdate:modelValue":a[10]||(a[10]=e=>B.value=e),loading:G.value,onChange:ia,"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue","loading"])])]),z("div",Kn,["offline"===e.account.status?(A(),F(o,{key:0,type:"primary",onClick:a[11]||(a[11]=e=>_.value=!0),loading:y.value},{default:M(()=>[N(i,null,{default:M(()=>[N(L(Le))]),_:1}),a[42]||(a[42]=$(" 设备复用重新登录 ",-1))]),_:1,__:[42]},8,["loading"])):(A(),F(o,{key:1,type:"default",onClick:ta,loading:h.value,class:"warning-button"},{default:M(()=>[N(i,null,{default:M(()=>[N(L(ea))]),_:1}),a[43]||(a[43]=$(" 断开连接 ",-1))]),_:1,__:[43]},8,["loading"])),"online"!==e.account.status?(A(),F(o,{key:2,type:"warning",onClick:qe,loading:w.value,plain:""},{default:M(()=>[N(i,null,{default:M(()=>[N(L(aa))]),_:1}),a[44]||(a[44]=$(" 开启心跳 ",-1))]),_:1,__:[44]},8,["loading"])):E("",!0),N(o,{type:"default",onClick:na,loading:x.value,class:"danger-button"},{default:M(()=>[N(i,null,{default:M(()=>[N(L(Ne))]),_:1}),a[45]||(a[45]=$(" 删除账号 ",-1))]),_:1,__:[45]},8,["loading"])])]),_:1})])):E("",!0)]),_:1},8,["modelValue","title"]),N(k,{modelValue:_.value,"onUpdate:modelValue":a[14]||(a[14]=e=>_.value=e),title:"设备复用重新登录",width:"400px","append-to-body":"","custom-class":"relogin-modal"},{footer:M(()=>[N(o,{onClick:a[13]||(a[13]=e=>_.value=!1)},{default:M(()=>a[47]||(a[47]=[$("取消",-1)])),_:1,__:[47]})]),default:M(()=>[b.value?(A(),j("div",qn,[z("div",Rn,[z("img",{src:b.value,alt:"登录二维码",class:"qr-image"},null,8,Bn)]),z("div",Qn,[z("p",{class:J(["qr-status-text",We()])},H(D.value),3)]),N(o,{onClick:Ee,loading:P.value},{default:M(()=>a[46]||(a[46]=[$(" 刷新二维码 ",-1)])),_:1,__:[46]},8,["loading"])])):(A(),j("div",Jn,[N(O,{rows:3,animated:""}),z("p",null,H(D.value),1)]))]),_:1},8,["modelValue"]),N(W,{modelValue:oe.value,"onUpdate:modelValue":a[15]||(a[15]=e=>oe.value=e),title:"代理管理",width:"85%",top:"5vh","append-to-body":"","custom-class":"proxy-management-dialog",center:""},{default:M(()=>[N(S,{onProxyUpdated:ve})]),_:1},8,["modelValue"])],64)}}}),[["__scopeId","data-v-a229b772"]]),Yn={class:"friend-request-notification"},Xn={class:"request-list"},Zn={key:0,class:"empty-state"},es={key:1},as={class:"request-header"},ls={class:"request-info"},ts={class:"nickname"},ns={key:0,class:"alias"},ss={class:"time"},is={key:0,class:"request-content"},os={class:"request-actions"},rs=v(D({__name:"FriendRequestNotification",setup(e){const a=U(!1),l=U([]),t=p(),n=Q(()=>l.value.length),s=()=>{a.value=!1},i=e=>d(null,null,function*(){var a;const n={id:Date.now().toString()+Math.random(),fromUserName:e.data.fromUserName,nickname:e.data.fromNickName,alias:e.data.alias,content:e.data.content,avatar:e.data.bigHeadImgUrl||e.data.smallHeadImgUrl||"",ticket:e.data.ticket,timestamp:e.timestamp||new Date};if(t.currentAccount)try{const e=yield g.getAutoAcceptFriendStatus({Wxid:t.currentAccount.wxid});if(e.Success&&(null==(a=e.Data)?void 0:a.isEnabled)){if((yield g.acceptFriendRequest({Wxid:t.currentAccount.wxid,V1:n.fromUserName,V2:n.ticket,Scene:3})).Success)return void be.success(`已自动同意 ${n.nickname} 的好友请求`)}}catch(s){}l.value.unshift(n),be.info(`${n.nickname} 请求添加你为好友：${n.content||"无验证消息"}`)});return T(()=>{_.on("friend_request",i)}),I(()=>{_.off("friend_request",i)}),(e,i)=>{const o=na,r=Te,u=xe,c=ta,m=Ce,v=sa;return A(),j("div",Yn,[N(c,{modelValue:a.value,"onUpdate:modelValue":i[0]||(i[0]=e=>a.value=e),title:"好友请求",direction:"rtl",size:"400px","before-close":s},{default:M(()=>[z("div",Xn,[0===l.value.length?(A(),j("div",Zn,[N(o,{description:"暂无好友请求"})])):(A(),j("div",es,[(A(!0),j(K,null,q(l.value,e=>{return A(),j("div",{key:e.id,class:"request-item"},[z("div",as,[N(r,{src:e.avatar,size:40},{default:M(()=>[$(H(e.nickname.charAt(0)),1)]),_:2},1032,["src"]),z("div",ls,[z("div",ts,H(e.nickname),1),e.alias?(A(),j("div",ns,"微信号："+H(e.alias),1)):E("",!0),z("div",ss,H((a=e.timestamp,a.toLocaleString("zh-CN",{month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}))),1)])]),e.content?(A(),j("div",is,[i[2]||(i[2]=z("div",{class:"content-label"},"验证消息：",-1)),z("p",null,H(e.content),1)])):E("",!0),z("div",os,[N(u,{type:"success",size:"small",onClick:a=>(e=>d(null,null,function*(){if(t.currentAccount){e.accepting=!0;try{const a=yield g.acceptFriendRequest({Wxid:t.currentAccount.wxid,V1:e.fromUserName,V2:e.ticket,Scene:3});if(a.Success){be.success("已同意好友请求");const a=l.value.findIndex(a=>a.id===e.id);a>-1&&l.value.splice(a,1)}else be.error(a.Message||"同意好友请求失败")}catch(a){be.error(a.message||"同意好友请求失败")}finally{e.accepting=!1}}else be.error("请先选择账号")}))(e),loading:e.accepting},{default:M(()=>i[3]||(i[3]=[$(" 同意 ",-1)])),_:2,__:[3]},1032,["onClick","loading"]),N(u,{type:"default",size:"small",onClick:a=>(e=>d(null,null,function*(){const a=l.value.findIndex(a=>a.id===e.id);a>-1&&(l.value.splice(a,1),be.success("已拒绝好友请求"))}))(e),loading:e.rejecting},{default:M(()=>i[4]||(i[4]=[$(" 拒绝 ",-1)])),_:2,__:[4]},1032,["onClick","loading"])])]);var a}),128))]))])]),_:1},8,["modelValue"]),N(v,{value:n.value,hidden:0===n.value,class:"notification-badge"},{default:M(()=>[N(u,{type:"primary",circle:"",size:"large",onClick:i[1]||(i[1]=e=>a.value=!0),class:"notification-button"},{default:M(()=>[N(m,null,{default:M(()=>[N(L(Oe))]),_:1})]),_:1})]),_:1},8,["value","hidden"])])}}}),[["__scopeId","data-v-10f09846"]]),us={class:"preset-file-cache-manager"},cs={class:"card-header"},ds={class:"header-actions"},ms={class:"stats-section"},vs={class:"preset-files-section"},ps={class:"dialog-footer"},fs=v(D({__name:"PresetFileCacheManager",setup(e){const a=U({size:0,files:[]}),l=U(""),t=U(!1),n=U(),s=U(pa()),i=Q(()=>s.value.length),o=U(!1),r=U({fileName:"",fileSize:0,attachId:"",cdnUrl:"",aesKey:"",appId:"",originalContent:""}),u={fileName:[{required:!0,message:"请输入文件名",trigger:"blur"}],fileSize:[{required:!0,message:"请输入文件大小",trigger:"blur"}],attachId:[{required:!0,message:"请输入附件ID",trigger:"blur"}],originalContent:[{required:!0,message:"请输入原始内容",trigger:"blur"}]},c=()=>{try{const e="wechat_file_cache",l=localStorage.getItem(e);if(l){const e=JSON.parse(l),t=new Map(e);a.value={size:t.size,files:Array.from(t.values()).map(e=>({fileName:e.fileName,fileSize:e.fileSize,cacheTime:e.cacheTime}))}}else a.value={size:0,files:[]}}catch(e){a.value={size:0,files:[]}}o.value=fa(),l.value=(new Date).toLocaleString("zh-CN")},m=(e,a)=>{try{const l="wechat_file_cache",t=localStorage.getItem(l);if(!t)return!1;const n=JSON.parse(t),s=new Map(n),i=`${e}_${a}`;return s.has(i)}catch(l){return!1}},v=()=>d(null,null,function*(){try{yield He.confirm("确定要重新初始化预置文件缓存吗？这将重新加载所有预置文件。","确认重新初始化",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),ga(),c(),be.success("预置文件缓存重新初始化成功")}catch(e){}}),p=()=>{ya(),be.info("调试信息已输出到控制台，请按F12查看")},f=()=>d(null,null,function*(){try{yield n.value.validate(),ha(r.value.fileName,r.value.fileSize,{fileName:r.value.fileName,fileSize:r.value.fileSize,originalContent:r.value.originalContent,attachId:r.value.attachId,cdnUrl:r.value.cdnUrl,aesKey:r.value.aesKey,appId:r.value.appId}),c(),t.value=!1,y(),be.success("预置文件缓存添加成功")}catch(e){be.error("添加失败，请检查输入信息")}}),g=()=>{t.value=!1,y()},y=()=>{var e;r.value={fileName:"",fileSize:0,attachId:"",cdnUrl:"",aesKey:"",appId:"",originalContent:""},null==(e=n.value)||e.clearValidate()};return T(()=>{c()}),(e,d)=>{const y=xe,h=ra,x=oa,w=ia,k=qe,_=Ie,C=Ke,S=Ue,V=he,P=fe,D=_e,U=pe,T=je;return A(),j("div",us,[N(S,null,{header:M(()=>[z("div",cs,[d[13]||(d[13]=z("span",null,"预置文件缓存管理",-1)),z("div",ds,[N(y,{type:"primary",icon:L(Le),onClick:c},{default:M(()=>d[9]||(d[9]=[$(" 刷新 ",-1)])),_:1,__:[9]},8,["icon"]),N(y,{type:"success",icon:L(ua),onClick:d[0]||(d[0]=e=>t.value=!0)},{default:M(()=>d[10]||(d[10]=[$(" 添加预置文件 ",-1)])),_:1,__:[10]},8,["icon"]),N(y,{type:"warning",icon:L(Je),onClick:v},{default:M(()=>d[11]||(d[11]=[$(" 重新初始化 ",-1)])),_:1,__:[11]},8,["icon"]),N(y,{type:"info",onClick:p},{default:M(()=>d[12]||(d[12]=[$(" 调试缓存 ",-1)])),_:1,__:[12]})])])]),default:M(()=>[z("div",ms,[N(w,{gutter:20},{default:M(()=>[N(x,{span:6},{default:M(()=>[N(h,{title:"预置文件数量",value:i.value},null,8,["value"])]),_:1}),N(x,{span:6},{default:M(()=>[N(h,{title:"缓存文件总数",value:a.value.size},null,8,["value"])]),_:1}),N(x,{span:6},{default:M(()=>[N(h,{title:"初始化状态",value:o.value?"已初始化":"未初始化"},null,8,["value"])]),_:1}),N(x,{span:6},{default:M(()=>[N(h,{title:"最后更新",value:l.value},null,8,["value"])]),_:1})]),_:1})]),z("div",vs,[d[14]||(d[14]=z("h3",null,"预置文件列表",-1)),N(C,{data:s.value,style:{width:"100%"}},{default:M(()=>[N(k,{prop:"fileName",label:"文件名","min-width":"200"}),N(k,{prop:"fileSize",label:"文件大小",width:"120"},{default:M(({row:e})=>[$(H(L(b)(e.fileSize)),1)]),_:1}),N(k,{prop:"attachId",label:"附件ID","min-width":"300","show-overflow-tooltip":""}),N(k,{label:"状态",width:"100"},{default:M(({row:e})=>[N(_,{type:m(e.fileName,e.fileSize)?"success":"warning"},{default:M(()=>[$(H(m(e.fileName,e.fileSize)?"已缓存":"未缓存"),1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data"])])]),_:1}),N(T,{modelValue:t.value,"onUpdate:modelValue":d[8]||(d[8]=e=>t.value=e),title:"添加预置文件缓存",width:"600px","before-close":g},{footer:M(()=>[z("div",ps,[N(y,{onClick:g},{default:M(()=>d[15]||(d[15]=[$("取消",-1)])),_:1,__:[15]}),N(y,{type:"primary",onClick:f},{default:M(()=>d[16]||(d[16]=[$("确定",-1)])),_:1,__:[16]})])]),default:M(()=>[N(U,{model:r.value,rules:u,ref_key:"addFormRef",ref:n,"label-width":"120px"},{default:M(()=>[N(P,{label:"文件名",prop:"fileName"},{default:M(()=>[N(V,{modelValue:r.value.fileName,"onUpdate:modelValue":d[1]||(d[1]=e=>r.value.fileName=e),placeholder:"请输入文件名，如：工作介绍.pptx"},null,8,["modelValue"])]),_:1}),N(P,{label:"文件大小",prop:"fileSize"},{default:M(()=>[N(D,{modelValue:r.value.fileSize,"onUpdate:modelValue":d[2]||(d[2]=e=>r.value.fileSize=e),min:1,placeholder:"文件大小（字节）"},null,8,["modelValue"])]),_:1}),N(P,{label:"附件ID",prop:"attachId"},{default:M(()=>[N(V,{modelValue:r.value.attachId,"onUpdate:modelValue":d[3]||(d[3]=e=>r.value.attachId=e),type:"textarea",rows:3,placeholder:"请输入附件ID"},null,8,["modelValue"])]),_:1}),N(P,{label:"CDN URL",prop:"cdnUrl"},{default:M(()=>[N(V,{modelValue:r.value.cdnUrl,"onUpdate:modelValue":d[4]||(d[4]=e=>r.value.cdnUrl=e),type:"textarea",rows:2,placeholder:"请输入CDN URL"},null,8,["modelValue"])]),_:1}),N(P,{label:"AES密钥",prop:"aesKey"},{default:M(()=>[N(V,{modelValue:r.value.aesKey,"onUpdate:modelValue":d[5]||(d[5]=e=>r.value.aesKey=e),placeholder:"请输入AES密钥"},null,8,["modelValue"])]),_:1}),N(P,{label:"应用ID",prop:"appId"},{default:M(()=>[N(V,{modelValue:r.value.appId,"onUpdate:modelValue":d[6]||(d[6]=e=>r.value.appId=e),placeholder:"请输入应用ID"},null,8,["modelValue"])]),_:1}),N(P,{label:"原始内容",prop:"originalContent"},{default:M(()=>[N(V,{modelValue:r.value.originalContent,"onUpdate:modelValue":d[7]||(d[7]=e=>r.value.originalContent=e),type:"textarea",rows:8,placeholder:"请输入完整的XML内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-11762e47"]]),gs={class:"dashboard-container"},ys={class:"header-content"},hs={class:"header-stats"},xs={class:"stat-item"},ws={class:"stat-value"},ks={class:"sidebar-header"},_s={class:"accounts-list"},bs={key:0,class:"empty-accounts"},Cs=["onClick"],Ss={class:"account-info"},Vs={class:"nickname"},Ps={key:0,class:"alias-inline"},Ds={class:"account-details"},Us={class:"account-wxid"},Ts={class:"status"},Is={class:"account-actions"},js={key:0,class:"welcome-content"},As={key:1,class:"account-dashboard"},Ns=v(D({__name:"dashboard",setup(e){const a=p(),l=de(),t=O(),n=U(!1),s=U(!1),i=U(!1),u=U(null),c=U("friends"),v=Q(()=>a.onlineAccounts.length),f=e=>{a.addAccount(e),setTimeout(()=>{g(),be.success("登录成功！"),le(()=>{})},2e3)},g=()=>{n.value=!1},y=e=>{a.addAccount(e),be.success("账号信息已更新")};return te(()=>l.query.tab,e=>{e&&"string"==typeof e&&(c.value=e)},{immediate:!0}),te(c,e=>{l.query.tab!==e&&t.push({path:l.path,query:r(o({},l.query),{tab:e})})}),T(()=>d(null,null,function*(){l.query.tab&&(c.value=l.query.tab);try{const e=yield a.fetchLoggedAccounts();e&&e.length>0?be.success(`成功获取 ${e.length} 个已登录账号`):be.info("当前没有已登录的账号")}catch(e){be.error(`获取已登录账号失败: ${e.message||"未知错误"}`)}})),(e,l)=>{const t=ca,o=Ce,r=xe,p=na,h=Te,x=Ie,w=ma,k=Fe,_=ve,b=me,C=va,S=da,V=je;return A(),j("div",gs,[N(t,{class:"header"},{default:M(()=>[z("div",ys,[l[9]||(l[9]=z("div",{class:"logo"},[z("div",{class:"logo-text"},"微信机器人管理系统")],-1)),z("div",hs,[z("div",xs,[l[8]||(l[8]=z("span",{class:"stat-label"},"在线账号",-1)),z("span",ws,H(v.value),1)])])])]),_:1}),N(S,{class:"main-container"},{default:M(()=>[N(w,{width:"300px",class:"accounts-sidebar"},{default:M(()=>[z("div",ks,[l[11]||(l[11]=z("h3",null,"在线账号",-1)),N(r,{type:"primary",size:"small",onClick:l[0]||(l[0]=e=>n.value=!0)},{default:M(()=>[N(o,null,{default:M(()=>[N(L(ua))]),_:1}),l[10]||(l[10]=$(" 添加账号 ",-1))]),_:1,__:[10]})]),z("div",_s,[0===L(a).accounts.length?(A(),j("div",bs,[N(p,{description:"暂无在线账号"},{default:M(()=>[N(r,{type:"primary",onClick:l[1]||(l[1]=e=>n.value=!0)},{default:M(()=>l[12]||(l[12]=[$("立即登录",-1)])),_:1,__:[12]})]),_:1})])):E("",!0),(A(!0),j(K,null,q(L(a).accounts,e=>{var t;return A(),j("div",{key:(null==e?void 0:e.wxid)||"unknown",class:J(["account-item",{active:(null==(t=L(a).currentAccount)?void 0:t.wxid)===(null==e?void 0:e.wxid)}]),onClick:l=>(e=>{if(e&&e.wxid)try{const l=a.currentAccount;a.setCurrentAccount(e.wxid),l&&l.wxid!==e.wxid?be.success(`已切换到账号：${e.nickname}，相关数据已重置`):be.success(`已切换到账号：${e.nickname}`)}catch(l){be.error("切换账号失败")}})(e)},[N(h,{src:(null==e?void 0:e.headUrl)||(null==e?void 0:e.avatar),size:40},{default:M(()=>{var a;return[$(H((null==(a=null==e?void 0:e.nickname)?void 0:a.charAt(0))||"?"),1)]}),_:2},1032,["src"]),z("div",Ss,[z("div",Vs,[$(H((null==e?void 0:e.nickname)||"未知账号")+" ",1),(null==e?void 0:e.alias)?(A(),j("span",Ps,"["+H(e.alias)+"]",1)):E("",!0)]),z("div",Ds,[z("div",Us,H((null==e?void 0:e.wxid)||""),1)]),z("div",Ts,[N(x,{type:"online"===(null==e?void 0:e.status)?"primary":"danger",size:"small",effect:"light"},{default:M(()=>[$(H("online"===(null==e?void 0:e.status)?"在线":"离线"),1)]),_:2},1032,["type"])])]),z("div",Is,[N(r,{type:"success",size:"small",onClick:R(a=>{return l=e.wxid,t="heartbeat",d(null,null,function*(){let e="";switch(t){case"auto":e="自动重连";break;case"awaken":e="唤醒登录";break;case"heartbeat":e="开启心跳";break;case"init":e="重新初始化";break;default:return void be.error("未知的重连类型")}try{be.info(`正在执行${e}...`);const a=yield m.autoHeartBeat(l);a.Success?(be.success(`${e}成功！`),setTimeout(()=>{},2e3)):be.error(`${e}失败: ${a.Message}`)}catch(a){be.error(`${e}失败: ${a.message}`)}});var l,t},["stop"]),title:"开启心跳"},{default:M(()=>[N(o,null,{default:M(()=>[N(L(Ge))]),_:1})]),_:2},1032,["onClick"]),N(r,{type:"primary",size:"small",onClick:R(a=>(e=>{u.value=e,i.value=!0})(e),["stop"])},{default:M(()=>l[13]||(l[13]=[$(" 管理 ",-1)])),_:2,__:[13]},1032,["onClick"])])],10,Cs)}),128))])]),_:1}),N(C,{class:"main-content"},{default:M(()=>[L(a).currentAccount?(A(),j("div",As,[N(b,{modelValue:c.value,"onUpdate:modelValue":l[3]||(l[3]=e=>c.value=e),class:"account-tabs"},{default:M(()=>[N(_,{label:"好友管理",name:"friends"},{default:M(()=>[N(Za,{account:L(a).currentAccount},null,8,["account"])]),_:1}),N(_,{label:"聊天功能",name:"chat"},{default:M(()=>[N(Gt,{account:L(a).currentAccount},null,8,["account"])]),_:1}),N(_,{label:"文件缓存管理",name:"fileCache"},{default:M(()=>[N(fs)]),_:1})]),_:1},8,["modelValue"])])):(A(),j("div",js,[N(k,{icon:"info",title:"欢迎使用微信机器人管理系统"},{"sub-title":M(()=>l[14]||(l[14]=[z("p",null,"请先登录微信账号或选择已登录的账号开始使用",-1)])),extra:M(()=>[N(r,{type:"primary",onClick:l[2]||(l[2]=e=>n.value=!0)},{default:M(()=>l[15]||(l[15]=[$("立即登录",-1)])),_:1,__:[15]})]),_:1})]))]),_:1})]),_:1}),N(V,{modelValue:n.value,"onUpdate:modelValue":l[4]||(l[4]=e=>n.value=e),title:"账号登录",width:"600px","close-on-click-modal":!1,"destroy-on-close":!0,onClose:g},{default:M(()=>[n.value?(A(),F(Da,{key:0,onLoginSuccess:f,onClose:g})):E("",!0)]),_:1},8,["modelValue"]),N(V,{modelValue:s.value,"onUpdate:modelValue":l[6]||(l[6]=e=>s.value=e),title:"账号管理",width:"800px"},{default:M(()=>[N(an,{onClose:l[5]||(l[5]=e=>s.value=!1)})]),_:1},8,["modelValue"]),N(Gn,{modelValue:i.value,"onUpdate:modelValue":l[7]||(l[7]=e=>i.value=e),account:u.value,onAccountUpdated:y},null,8,["modelValue","account"]),N(rs)])}}}),[["__scopeId","data-v-da45328c"]]);export{Ns as default};

<!doctype html>
<html lang="en" data-theme="light" class="light">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="color-scheme" content="light" />
    <title>Element Plus Vite Starter</title>
    <!-- element css cdn, if you use custom theme, remove it. -->
    <!-- <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/element-plus/dist/index.css"
    /> -->
    <script type="module" crossorigin src="/js/index-BV89XUvk.js"></script>
    <link rel="modulepreload" crossorigin href="/js/vue-C-lRF7aZ.js">
    <link rel="modulepreload" crossorigin href="/js/element-plus-DAEtRXw7.js">
    <link rel="modulepreload" crossorigin href="/js/utils-XWYVm-q4.js">
    <link rel="modulepreload" crossorigin href="/js/components-BbzPJspU.js">
    <link rel="stylesheet" crossorigin href="/assets/components-CXH3rzJ9.css">
    <link rel="stylesheet" crossorigin href="/assets/index-CKJrgkw_.css">
  </head>
  <body class="light" data-theme="light">
    <div id="app"></div>

  </body>
</html>

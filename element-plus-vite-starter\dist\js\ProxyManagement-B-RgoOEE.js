var e=(e,a,t)=>new Promise((l,o)=>{var r=e=>{try{n(t.next(e))}catch(a){o(a)}},s=e=>{try{n(t.throw(e))}catch(a){o(a)}},n=e=>e.done?l(e.value):Promise.resolve(e.value).then(r,s);n((t=t.apply(e,a)).next())});import{e as a,_ as t}from"./components-BbzPJspU.js";/* empty css              */import{c as l,a as o,a4 as r,G as s,M as n,a1 as u,a2 as i,a3 as d,q as c,r as p,s as y,z as m,T as v,aa as f,O as g,D as x,ab as _,i as h,R as P,E as b,J as w,N as C}from"./element-plus-DAEtRXw7.js";import{t as k,r as z,V,c as $,f as L,v as S,x as T,A as U,M as j,E as I,J as B,u as D,L as G,ah as A,F as E,D as M,K as J}from"./vue-C-lRF7aZ.js";const K={importProxies:e=>a.post("/Proxy/ImportProxies",e),getProxyList:e=>a.get("/Proxy/GetProxyList",{params:e}),getProxyStats:()=>a.get("/Proxy/GetProxyStats"),testProxies:e=>a.post("/Proxy/TestProxies",e),deleteProxy:e=>a.delete(`/Proxy/DeleteProxy/${e}`),clearProxies:()=>a.post("/Proxy/ClearProxies"),updateProxyCountries:()=>a.post("/Proxy/UpdateProxyCountries"),getAvailableProxies:e=>a.get("/Proxy/GetProxyList",{params:{status:"active",country:e,page_size:100}})},O={active:{label:"可用",color:"success",icon:"🟢"},inactive:{label:"未测试",color:"info",icon:"⚪"},testing:{label:"测试中",color:"warning",icon:"🟡"},error:{label:"错误",color:"danger",icon:"🔴"}},R=e=>{const a=O[e.status]||O.inactive,t=e.response_time>0?` (${e.response_time}ms)`:"",l=e.country?` [${e.country}]`:"";return`${a.icon} ${e.ip}:${e.port}${l}${t}`},q={class:"proxy-management"},F={class:"proxy-management-content"},N={class:"card-header"},Q={class:"header-actions"},H={class:"stats-section"},W={class:"filter-section"},X={class:"proxy-list table-section"},Y={key:0},Z={key:1},ee={class:"pagination"},ae={class:"import-dialog"},te={class:"import-actions"},le=t(k({__name:"ProxyManagement",emits:["proxy-updated"],setup(a,{emit:t}){const k=t,R=z(!1),le=z(!1),oe=z(!1),re=z([]),se=z([]),ne=V({total:0,active:0,inactive:0,testing:0,error:0}),ue=V({status:"",country:"",keyword:""}),ie=V({page:1,pageSize:20,total:0}),de=V({proxyList:"",format:"ip|port|username|password|expire"}),ce=$(()=>(e=>{const a=new Set;return e.forEach(e=>{e.country&&a.add(e.country)}),Array.from(a).sort()})(re.value)),pe=e=>{const a=O[e];return(null==a?void 0:a.color)||"info"},ye=e=>{const a=O[e];return(null==a?void 0:a.label)||"未知"},me=()=>e(null,null,function*(){R.value=!0;try{const e=yield K.getProxyList({page:ie.page,page_size:ie.pageSize,status:ue.status,country:ue.country,keyword:ue.keyword});0===e.code&&(re.value=e.data.list,ie.total=e.data.total)}catch(e){b.error("获取代理列表失败")}finally{R.value=!1}}),ve=()=>e(null,null,function*(){try{const e=yield K.getProxyStats();0===e.code&&Object.assign(ne,e.data)}catch(e){}}),fe=()=>{ie.page=1,me()},ge=e=>{ie.page=e,me()},xe=e=>{ie.pageSize=e,ie.page=1,me()},_e=e=>{se.value=e},he=()=>e(null,null,function*(){if(de.proxyList.trim()){le.value=!0;try{const e=yield K.importProxies({proxy_list:de.proxyList,format:de.format});0===e.code?(b.success(`导入成功：${e.data.success_count}个代理`),oe.value=!1,de.proxyList="",me(),ve(),k("proxy-updated")):b.error(e.message||"导入失败")}catch(e){b.error("导入代理失败")}finally{le.value=!1}}else b.warning("请输入代理列表")}),Pe=()=>e(null,null,function*(){if(0!==se.value.length)try{const e=se.value.map(e=>e.id);0===(yield K.testProxies({proxy_ids:e})).code&&(b.success("代理测试已开始"),me(),k("proxy-updated"),setTimeout(()=>{me(),ve(),k("proxy-updated"),b.success("代理测试完成")},5e3))}catch(e){b.error("测试代理失败")}else b.warning("请选择要测试的代理")}),be=()=>e(null,null,function*(){if(0!==se.value.length)try{yield w.confirm(`确定要删除选中的 ${se.value.length} 个代理吗？此操作不可恢复。`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const a=se.value.map(e=>e.id);let t=0,l=0;for(const o of a)try{0===(yield K.deleteProxy(o)).code?t++:l++}catch(e){l++}t>0?(b.success(`成功删除 ${t} 个代理${l>0?`，失败 ${l} 个`:""}`),me(),k("proxy-updated")):b.error("删除失败")}catch(e){"cancel"!==e&&b.error("批量删除代理失败")}else b.warning("请先选择要删除的代理")});return L(()=>{me(),ve()}),(a,t)=>{const z=o,V=l,$=d,L=i,O=u,we=p,Ce=c,ke=y,ze=g,Ve=x,$e=C,Le=_,Se=P,Te=h,Ue=f;return T(),S("div",q,[U("div",F,[U("div",N,[t[12]||(t[12]=U("span",null,"代理管理",-1)),U("div",Q,[j(V,{type:"primary",onClick:t[0]||(t[0]=e=>oe.value=!0)},{default:I(()=>[j(z,null,{default:I(()=>[j(D(r))]),_:1}),t[9]||(t[9]=B(" 批量导入 ",-1))]),_:1,__:[9]}),j(V,{type:"danger",onClick:be,disabled:0===se.value.length},{default:I(()=>[j(z,null,{default:I(()=>[j(D(s))]),_:1}),t[10]||(t[10]=B(" 批量删除 ",-1))]),_:1,__:[10]},8,["disabled"]),j(V,{onClick:me},{default:I(()=>[j(z,null,{default:I(()=>[j(D(n))]),_:1}),t[11]||(t[11]=B(" 刷新 ",-1))]),_:1,__:[11]})])]),U("div",H,[j(O,{gutter:16},{default:I(()=>[j(L,{span:6},{default:I(()=>[j($,{title:"总数",value:ne.total},null,8,["value"])]),_:1}),j(L,{span:6},{default:I(()=>[j($,{title:"可用",value:ne.active,"value-style":"color: #67c23a"},null,8,["value"])]),_:1}),j(L,{span:6},{default:I(()=>[j($,{title:"未测试",value:ne.inactive,"value-style":"color: #909399"},null,8,["value"])]),_:1}),j(L,{span:6},{default:I(()=>[j($,{title:"错误",value:ne.error,"value-style":"color: #f56c6c"},null,8,["value"])]),_:1})]),_:1})]),U("div",W,[j(O,{gutter:16},{default:I(()=>[j(L,{span:6},{default:I(()=>[j(Ce,{modelValue:ue.status,"onUpdate:modelValue":t[1]||(t[1]=e=>ue.status=e),placeholder:"状态筛选",clearable:"",onChange:fe},{default:I(()=>[j(we,{label:"全部",value:""}),j(we,{label:"可用",value:"active"}),j(we,{label:"未测试",value:"inactive"}),j(we,{label:"测试中",value:"testing"}),j(we,{label:"错误",value:"error"})]),_:1},8,["modelValue"])]),_:1}),j(L,{span:6},{default:I(()=>[j(Ce,{modelValue:ue.country,"onUpdate:modelValue":t[2]||(t[2]=e=>ue.country=e),placeholder:"地区筛选",clearable:"",onChange:fe},{default:I(()=>[j(we,{label:"全部",value:""}),(T(!0),S(G,null,A(ce.value,e=>(T(),M(we,{key:e,label:e,value:e},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),j(L,{span:8},{default:I(()=>[j(ke,{modelValue:ue.keyword,"onUpdate:modelValue":t[3]||(t[3]=e=>ue.keyword=e),placeholder:"搜索IP或用户名",clearable:"",onInput:fe},{prefix:I(()=>[j(z,null,{default:I(()=>[j(D(m))]),_:1})]),_:1},8,["modelValue"])]),_:1}),j(L,{span:4},{default:I(()=>[j(V,{type:"warning",onClick:Pe,disabled:0===se.value.length},{default:I(()=>[j(z,null,{default:I(()=>[j(D(v))]),_:1}),t[13]||(t[13]=B(" 测试选中 ",-1))]),_:1,__:[13]},8,["disabled"])]),_:1})]),_:1})]),U("div",X,[E((T(),M($e,{data:re.value,onSelectionChange:_e,stripe:"",height:"450","table-layout":"auto",style:{width:"100%"},"header-cell-style":{backgroundColor:"#f5f7fa",color:"#606266"}},{default:I(()=>[j(ze,{type:"selection",width:"55"}),j(ze,{label:"状态","min-width":"80"},{default:I(({row:e})=>[j(Ve,{type:pe(e.status),size:"small"},{default:I(()=>[B(J(ye(e.status)),1)]),_:2},1032,["type"])]),_:1}),j(ze,{prop:"ip",label:"IP地址","min-width":"150"}),j(ze,{prop:"port",label:"端口","min-width":"80"}),j(ze,{prop:"username",label:"用户名","min-width":"120"}),j(ze,{prop:"country",label:"地区","min-width":"180"},{default:I(({row:e})=>[U("span",null,J(e.country||"-"),1)]),_:1}),j(ze,{label:"响应时间","min-width":"100"},{default:I(({row:e})=>[e.response_time>0?(T(),S("span",Y,J(e.response_time)+"ms",1)):(T(),S("span",Z,"-"))]),_:1}),j(ze,{prop:"expire_date",label:"过期时间","min-width":"120"}),j(ze,{label:"操作",width:"150",fixed:"right"},{default:I(({row:a})=>[j(V,{size:"small",onClick:t=>{return l=a.id,e(null,null,function*(){try{0===(yield K.testProxies({proxy_ids:[l]})).code&&(b.success("代理测试已开始"),me(),k("proxy-updated"),setTimeout(()=>{me(),ve(),k("proxy-updated")},5e3))}catch(e){b.error("测试代理失败")}});var l}},{default:I(()=>t[14]||(t[14]=[B("测试",-1)])),_:2,__:[14]},1032,["onClick"]),j(V,{size:"small",type:"danger",onClick:t=>{return l=a.id,e(null,null,function*(){try{yield w.confirm("确定要删除这个代理吗？","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),0===(yield K.deleteProxy(l)).code&&(b.success("删除成功"),me(),ve(),k("proxy-updated"))}catch(e){"cancel"!==e&&b.error("删除代理失败")}});var l}},{default:I(()=>t[15]||(t[15]=[B("删除",-1)])),_:2,__:[15]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Ue,R.value]]),U("div",ee,[j(Le,{"current-page":ie.page,"onUpdate:currentPage":t[4]||(t[4]=e=>ie.page=e),"page-size":ie.pageSize,"onUpdate:pageSize":t[5]||(t[5]=e=>ie.pageSize=e),total:ie.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:xe,onCurrentChange:ge},null,8,["current-page","page-size","total"])])])]),j(Te,{modelValue:oe.value,"onUpdate:modelValue":t[8]||(t[8]=e=>oe.value=e),title:"批量导入代理",width:"600px"},{default:I(()=>[U("div",ae,[j(Se,{title:"导入格式说明",type:"info",closable:!1,style:{"margin-bottom":"16px"}},{default:I(()=>t[16]||(t[16]=[U("p",null,"每行一个代理，格式：IP|端口|用户名|密码|过期时间",-1),U("p",null,"示例：*************|18474|LT9RQDK6TB|03399060|2025-08-31",-1)])),_:1}),j(ke,{modelValue:de.proxyList,"onUpdate:modelValue":t[6]||(t[6]=e=>de.proxyList=e),type:"textarea",rows:10,placeholder:"请粘贴代理列表..."},null,8,["modelValue"]),U("div",te,[j(V,{onClick:t[7]||(t[7]=e=>oe.value=!1)},{default:I(()=>t[17]||(t[17]=[B("取消",-1)])),_:1,__:[17]}),j(V,{type:"primary",onClick:he,loading:le.value},{default:I(()=>t[18]||(t[18]=[B(" 导入 ",-1)])),_:1,__:[18]},8,["loading"])])])]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-61894ab7"]]);export{le as P,R as g,K as p};

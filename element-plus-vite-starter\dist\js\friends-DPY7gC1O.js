const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["js/components-BbzPJspU.js","js/element-plus-DAEtRXw7.js","js/vue-C-lRF7aZ.js","js/utils-XWYVm-q4.js","assets/components-CXH3rzJ9.css"])))=>i.map(i=>d[i]);
var e=(e,a,l)=>new Promise((t,s)=>{var n=e=>{try{i(l.next(e))}catch(a){s(a)}},r=e=>{try{i(l.throw(e))}catch(a){s(a)}},i=e=>e.done?t(e.value):Promise.resolve(e.value).then(n,r);i((l=l.apply(e,a)).next())});import{_ as a}from"./index-grLkMC3o.js";import{u as l,f as t,_ as s}from"./components-BbzPJspU.js";/* empty css              */import{u as n}from"./avatar-CHH9M8bf.js";import{t as r,r as i,V as c,f as u,az as d,c as o,v as f,A as v,L as m,ah as _,u as p,M as g,E as h,x as y,J as x,K as w,H as k,G as b}from"./vue-C-lRF7aZ.js";import{E as A,c as V,k as C,a as S,M as j,m as D,s as U,z as $,n as z,o as F,P as T,Q as M,a4 as N,R as P,J as E,K as I,C as L,x as G,ac as O,G as R}from"./element-plus-DAEtRXw7.js";import"./utils-XWYVm-q4.js";const H={class:"friends-container"},J={class:"account-sidebar"},K={class:"account-list"},W=["onClick"],q={class:"account-info"},Q={class:"nickname"},B={class:"status"},X={class:"sidebar-footer"},Y={class:"main-content"},Z={class:"content-header"},ee={class:"header-actions"},ae={class:"friends-list-panel"},le={class:"list-header"},te={class:"friend-stats"},se={class:"friends-grid"},ne={class:"friend-info"},re={class:"friend-name"},ie={class:"friend-wxid"},ce={class:"friend-meta"},ue={key:0,class:"signature"},de={class:"friend-actions"},oe={class:"search-panel"},fe={key:0,class:"search-results"},ve={class:"result-list"},me={class:"contact-info"},_e={class:"contact-name"},pe={class:"contact-wxid"},ge={class:"contact-signature"},he={class:"batch-panel"},ye=s(r({__name:"friends",setup(s){const r=3,ye=d(),xe=l(),we=n(),ke=i("list"),be=i("");i([]);const Ae=c({keyword:"",searchType:"wxid"}),Ve=c({targets:"",message:"你好，我想加你为好友"}),Ce=i([]),Se=i(!1);u(()=>{xe.isLoggedIn?De():ye.push("/login")});const je=o(()=>{if(!xe.currentAccount)return[];const e=we.currentFriends(xe.currentAccount.wxid);return be.value?e.filter(e=>e.nickname.includes(be.value)||e.wxid.includes(be.value)||e.remark&&e.remark.includes(be.value)):e}),De=()=>e(null,null,function*(){if(xe.currentAccount)try{yield we.loadFriends(xe.currentAccount.wxid,!0),A.success("好友列表加载完成")}catch(e){A.error("加载好友列表失败")}}),Ue=()=>e(null,null,function*(){var e,a,l,t,s,n;if(Ae.keyword.trim())if(xe.currentAccount){Se.value=!0,Ce.value=[];try{const r=yield we.searchContact(xe.currentAccount.wxid,Ae.keyword.trim());if(r.Success&&r.Data){const i=(null==(a=null==(e=r.Data.UserName)?void 0:e.string)?void 0:a.includes("@stranger"))||!1;Ce.value=[{wxid:i?(null==(l=r.Data.Pyinitial)?void 0:l.string)||"":(null==(t=r.Data.UserName)?void 0:t.string)||"",nickname:(null==(s=r.Data.NickName)?void 0:s.string)||"",avatar:r.Data.SmallHeadImgUrl||"",signature:r.Data.Signature||"",sex:r.Data.Sex||0,v1:(null==(n=r.Data.UserName)?void 0:n.string)||"",v2:r.Data.AntispamTicket||""}],A.success("搜索完成")}else A.warning(r.Message||"未找到相关用户")}catch(r){A.error("搜索失败")}finally{Se.value=!1}}else A.error("请先选择账号");else A.warning("请输入搜索关键词")}),$e=()=>e(null,null,function*(){if(!Ve.targets.trim())return void A.warning("请输入要添加的好友列表");if(!xe.currentAccount)return void A.error("请先选择账号");const e=Ve.targets.split("\n").map(e=>e.trim()).filter(e=>e).map(e=>({identifier:e,message:Ve.message}));if(0!==e.length)try{const a=yield we.batchAddFriends(xe.currentAccount.wxid,e),l=a.filter(e=>e.success).length,t=a.length-l;A.success(`批量添加完成：成功 ${l} 个，失败 ${t} 个`);const s=a.filter(e=>!e.success);if(s.length>0){const e=s.map(e=>`${e.target}: ${e.message}`).join("\n");E.alert(e,"失败详情",{type:"warning"})}}catch(a){A.error("批量添加失败")}else A.warning("请输入有效的好友标识")}),ze=()=>{ye.push("/dashboard")},Fe=e=>{switch(e){case 1:return"男";case 2:return"女";default:return"未知"}},Te=e=>null==e?"":"string"==typeof e?e:"number"==typeof e?String(e):"object"==typeof e?0===Object.keys(e).length?"":void 0!==e.string&&null!==e.string&&""!==e.string?String(e.string):void 0!==e.value&&null!==e.value&&""!==e.value?String(e.value):void 0!==e.text&&null!==e.text&&""!==e.text?String(e.text):"":String(e);return(l,s)=>{const n=S,i=L,c=V,u=U,d=D,o=M,Me=T,Ne=F,Pe=z,Ee=P,Ie=C;return y(),f("div",H,[v("div",J,[s[7]||(s[7]=v("div",{class:"sidebar-header"},[v("h3",null,"选择账号")],-1)),v("div",K,[(y(!0),f(m,null,_(p(xe).accounts,a=>{var l;return y(),f("div",{key:a.wxid,class:b(["account-item",{active:(null==(l=p(xe).currentAccount)?void 0:l.wxid)===a.wxid}]),onClick:l=>(a=>e(null,null,function*(){var e;(null==(e=xe.currentAccount)?void 0:e.wxid)!==a.wxid&&(friends.value=[],filteredFriends.value=[],be.value="",A.info(`已切换到账号：${a.nickname}，正在获取通讯录...`)),xe.setCurrentAccount(a.wxid),yield De()}))(a)},[g(i,{src:a.avatar,size:32},{default:h(()=>[g(n,null,{default:h(()=>[g(p(I))]),_:1})]),_:2},1032,["src"]),v("div",q,[v("div",Q,w(a.nickname),1),v("div",B,w("online"===a.status?"在线":"离线"),1)])],10,W)}),128))]),v("div",X,[g(c,{onClick:ze,link:""},{default:h(()=>s[6]||(s[6]=[x(" 返回聊天 ",-1)])),_:1,__:[6]})])]),v("div",Y,[v("div",Z,[s[9]||(s[9]=v("h2",null,"好友管理",-1)),v("div",ee,[g(c,{onClick:De,loading:p(we).isLoading},{default:h(()=>[g(n,null,{default:h(()=>[g(p(j))]),_:1}),s[8]||(s[8]=x(" 刷新 ",-1))]),_:1,__:[8]},8,["loading"])])]),g(Ie,{modelValue:ke.value,"onUpdate:modelValue":s[5]||(s[5]=e=>ke.value=e),class:"content-tabs"},{default:h(()=>[g(d,{label:"好友列表",name:"list"},{default:h(()=>[v("div",ae,[v("div",le,[g(u,{modelValue:be.value,"onUpdate:modelValue":s[0]||(s[0]=e=>be.value=e),placeholder:"搜索好友...",style:{width:"300px"}},{prefix:h(()=>[g(n,null,{default:h(()=>[g(p($))]),_:1})]),_:1},8,["modelValue"]),v("div",te," 共 "+w(je.value.length)+" 个好友 ",1)]),v("div",se,[(y(!0),f(m,null,_(je.value,l=>(y(),f("div",{key:l.wxid,class:"friend-card"},[g(i,{src:l.avatar,size:60},{default:h(()=>[g(n,null,{default:h(()=>[g(p(G))]),_:1})]),_:2},1032,["src"]),v("div",ne,[v("div",re,w(Te(l.remark)||Te(l.nickname)),1),v("div",ie,w(Te(l.wxid)),1),v("div",ce,[x(w(Fe(l.sex))+" ",1),Te(l.signature)?(y(),f("span",ue," | "+w(Te(l.signature)),1)):k("",!0)])]),v("div",de,[g(c,{size:"small",onClick:t=>(l=>e(null,null,function*(){if(xe.currentAccount)try{const{useChatStore:t}=yield a(()=>e(null,null,function*(){const{useChatStore:e}=yield import("./components-BbzPJspU.js").then(e=>e.g);return{useChatStore:e}}),__vite__mapDeps([0,1,2,3,4])),s=t(),n=s.createOrGetSession(l);s.setCurrentSession(n.id),yield ye.push("/dashboard?tab=chat"),A.success(`已开始与 ${l.nickname} 的聊天`)}catch(t){A.error("开始聊天失败，请重试")}else A.error("请先选择账号")}))(l)},{default:h(()=>[g(n,null,{default:h(()=>[g(p(O))]),_:1}),s[10]||(s[10]=x(" 聊天 ",-1))]),_:2,__:[10]},1032,["onClick"]),g(c,{size:"small",type:"danger",onClick:a=>(a=>e(null,null,function*(){try{if(yield E.confirm(`确定要删除好友 ${a.nickname} 吗？`,"确认删除",{type:"warning"}),!xe.currentAccount)return void A.error("请先选择账号");const e=yield t.deleteFriend({Wxid:xe.currentAccount.wxid,ToWxid:a.wxid});e.Success?(A.success("删除好友成功"),we.removeFriend(xe.currentAccount.wxid,a.wxid),De()):A.error(e.Message||"删除好友失败")}catch(e){"cancel"!==e&&A.error(e.message||"删除好友失败")}}))(l)},{default:h(()=>[g(n,null,{default:h(()=>[g(p(R))]),_:1}),s[11]||(s[11]=x(" 删除 ",-1))]),_:2,__:[11]},1032,["onClick"])])]))),128))])])]),_:1}),g(d,{label:"搜索添加",name:"search"},{default:h(()=>[v("div",oe,[g(Pe,{model:Ae,"label-width":"100px"},{default:h(()=>[g(Ne,{label:"搜索类型"},{default:h(()=>[g(Me,{modelValue:Ae.searchType,"onUpdate:modelValue":s[1]||(s[1]=e=>Ae.searchType=e)},{default:h(()=>[g(o,{value:"wxid"},{default:h(()=>s[12]||(s[12]=[x("微信号",-1)])),_:1,__:[12]}),g(o,{value:"phone"},{default:h(()=>s[13]||(s[13]=[x("手机号",-1)])),_:1,__:[13]})]),_:1},8,["modelValue"])]),_:1}),g(Ne,{label:"关键词"},{default:h(()=>[g(u,{modelValue:Ae.keyword,"onUpdate:modelValue":s[2]||(s[2]=e=>Ae.keyword=e),placeholder:"wxid"===Ae.searchType?"输入微信号":"输入手机号",style:{width:"300px"}},{append:h(()=>[g(c,{onClick:Ue,loading:Se.value},{default:h(()=>[g(n,null,{default:h(()=>[g(p($))]),_:1}),s[14]||(s[14]=x(" 搜索 ",-1))]),_:1,__:[14]},8,["loading"])]),_:1},8,["modelValue","placeholder"])]),_:1})]),_:1},8,["model"]),Ce.value.length>0?(y(),f("div",fe,[s[16]||(s[16]=v("h4",null,"搜索结果",-1)),v("div",ve,[(y(!0),f(m,null,_(Ce.value,a=>(y(),f("div",{key:a.wxid,class:"result-item"},[g(i,{src:a.avatar,size:50},{default:h(()=>[g(n,null,{default:h(()=>[g(p(G))]),_:1})]),_:2},1032,["src"]),v("div",me,[v("div",_e,w(Te(a.nickname)),1),v("div",pe,w(Te(a.wxid)),1),v("div",ge,w(Te(a.signature)||"暂无签名"),1)]),g(c,{type:"primary",onClick:l=>((a,l)=>e(null,null,function*(){if(xe.currentAccount)if(a.v1&&a.v2)try{const e=l||`你好，我是${xe.currentAccount.nickname}`,t=yield we.sendFriendRequest(xe.currentAccount.wxid,a.v1,a.v2,e,r);t.Success?A.success("好友请求发送成功"):A.error(t.Message||"发送好友请求失败")}catch(e){A.error(e.message||"发送好友请求失败")}else A.error("联系人信息不完整，请重新搜索");else A.error("请先选择账号")}))(a)},{default:h(()=>[g(n,null,{default:h(()=>[g(p(N))]),_:1}),s[15]||(s[15]=x(" 添加好友 ",-1))]),_:2,__:[15]},1032,["onClick"])]))),128))])])):k("",!0)])]),_:1}),g(d,{label:"批量添加",name:"batch"},{default:h(()=>[v("div",he,[g(Pe,{model:Ve,"label-width":"100px"},{default:h(()=>[g(Ne,{label:"验证消息"},{default:h(()=>[g(u,{modelValue:Ve.message,"onUpdate:modelValue":s[3]||(s[3]=e=>Ve.message=e),placeholder:"输入好友验证消息",style:{width:"400px"}},null,8,["modelValue"])]),_:1}),g(Ne,{label:"好友列表"},{default:h(()=>[g(u,{modelValue:Ve.targets,"onUpdate:modelValue":s[4]||(s[4]=e=>Ve.targets=e),type:"textarea",rows:10,placeholder:"每行一个微信号或手机号，例如：\nwxid_123456\n13800138000\nwxid_abcdef",style:{width:"400px"}},null,8,["modelValue"])]),_:1}),g(Ne,null,{default:h(()=>[g(c,{type:"primary",onClick:$e,loading:p(we).isAdding},{default:h(()=>[g(n,null,{default:h(()=>[g(p(N))]),_:1}),s[17]||(s[17]=x(" 开始批量添加 ",-1))]),_:1,__:[17]},8,["loading"])]),_:1})]),_:1},8,["model"]),g(Ee,{title:"批量添加说明",type:"info",closable:!1,"show-icon":""},{default:h(()=>s[18]||(s[18]=[v("p",null,"1. 每行输入一个微信号或手机号",-1),v("p",null,"2. 系统会自动搜索并发送好友请求",-1),v("p",null,"3. 为避免频率限制，每个请求间隔2秒",-1),v("p",null,"4. 建议单次添加不超过50个好友",-1)])),_:1,__:[18]})])]),_:1})]),_:1},8,["modelValue"])])])}}}),[["__scopeId","data-v-6d9a568a"]]);export{ye as default};

const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["js/dashboard-Bevke8u2.js","js/components-BbzPJspU.js","js/element-plus-DAEtRXw7.js","js/vue-C-lRF7aZ.js","js/utils-XWYVm-q4.js","assets/components-CXH3rzJ9.css","js/ProxyManagement-B-RgoOEE.js","assets/ProxyManagement-CIdDq8Ha.css","assets/radio-kO0NOkSt.css","js/avatar-CHH9M8bf.js","assets/avatar-x7RIw_Og.css","assets/dashboard-TZRs3sub.css","js/login-GQbWWj55.js","assets/login-DgMbY9ds.css","js/friends-BxLvEk3H.js","assets/friends-CqCdnsu5.css"])))=>i.map(i=>d[i]);
var t=Object.defineProperty,e=Object.defineProperties,n=Object.getOwnPropertyDescriptors,a=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable,o=(e,n,a)=>n in e?t(e,n,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[n]=a,s=(t,e)=>{for(var n in e||(e={}))r.call(e,n)&&o(t,n,e[n]);if(a)for(var n of a(e))c.call(e,n)&&o(t,n,e[n]);return t},i=(t,a)=>e(t,n(a));import{t as l,f as d,am as m,v as u,x as f,M as p,E as h,aw as y,ax as b,ay as g,as as w}from"./vue-C-lRF7aZ.js";import{u as v,c as _}from"./components-BbzPJspU.js";import{j as E}from"./element-plus-DAEtRXw7.js";import"./utils-XWYVm-q4.js";!function(){const t=document.createElement("link").relList;if(!(t&&t.supports&&t.supports("modulepreload"))){for(const t of document.querySelectorAll('link[rel="modulepreload"]'))e(t);new MutationObserver(t=>{for(const n of t)if("childList"===n.type)for(const t of n.addedNodes)"LINK"===t.tagName&&"modulepreload"===t.rel&&e(t)}).observe(document,{childList:!0,subtree:!0})}function e(t){if(t.ep)return;t.ep=!0;const e=function(t){const e={};return t.integrity&&(e.integrity=t.integrity),t.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),"use-credentials"===t.crossOrigin?e.credentials="include":"anonymous"===t.crossOrigin?e.credentials="omit":e.credentials="same-origin",e}(t);fetch(t.href,e)}}();const S={},O=function(t,e,n){let a=Promise.resolve();if(e&&e.length>0){let t=function(t){return Promise.all(t.map(t=>Promise.resolve(t).then(t=>({status:"fulfilled",value:t}),t=>({status:"rejected",reason:t}))))};document.getElementsByTagName("link");const n=document.querySelector("meta[property=csp-nonce]"),r=(null==n?void 0:n.nonce)||(null==n?void 0:n.getAttribute("nonce"));a=t(e.map(t=>{if((t=function(t){return"/"+t}(t))in S)return;S[t]=!0;const e=t.endsWith(".css"),n=e?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${t}"]${n}`))return;const a=document.createElement("link");return a.rel=e?"stylesheet":"modulepreload",e||(a.as="script"),a.crossOrigin="",a.href=t,r&&a.setAttribute("nonce",r),document.head.appendChild(a),e?new Promise((e,n)=>{a.addEventListener("load",e),a.addEventListener("error",()=>n(new Error(`Unable to preload CSS for ${t}`)))}):void 0}))}function r(t){const e=new Event("vite:preloadError",{cancelable:!0});if(e.payload=t,window.dispatchEvent(e),!e.defaultPrevented)throw t}return a.then(e=>{for(const t of e||[])"rejected"===t.status&&r(t.reason);return t().catch(r)})},x={id:"app",class:"app-container"},N=l({__name:"App",setup(t){const e=v();return d(()=>{return t=null,n=null,a=function*(){try{yield e.fetchLoggedAccounts()}catch(t){}},new Promise((e,r)=>{var c=t=>{try{s(a.next(t))}catch(e){r(e)}},o=t=>{try{s(a.throw(t))}catch(e){r(e)}},s=t=>t.done?e(t.value):Promise.resolve(t.value).then(c,o);s((a=a.apply(t,n)).next())});var t,n,a}),(t,e)=>{const n=m("router-view"),a=E;return f(),u("div",x,[p(a,{namespace:"ep"},{default:h(()=>[p(n)]),_:1})])}}}),P=y(),L=[["工作介绍.pptx_140967",{fileName:"工作介绍.pptx",fileSize:140967,originalContent:'<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="wx6618f1cfc6c132f8" sdkver="0">\n\t\t<title>工作介绍.pptx</title>\n\t\t<des />\n\t\t<action />\n\t\t<type>6</type>\n\t\t<showtype>0</showtype>\n\t\t<soundtype>0</soundtype>\n\t\t<mediatagname />\n\t\t<messageext />\n\t\t<messageaction />\n\t\t<content />\n\t\t<contentattr>0</contentattr>\n\t\t<url />\n\t\t<lowurl />\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<songalbumurl />\n\t\t<songlyric />\n\t\t<template_id />\n\t\t<appattach>\n\t\t\t<totallen>140967</totallen>\n\t\t\t<attachid>@cdn_3057020100044b30490201000204bf693cc002032dcfbe0204914cf8240204688ecc76042430336535653236632d396666302d346430622d393463622d3239653937663635336164380204051400050201000405004c50ba00_3effa7f808753e85c64ea67361266300_1</attachid>\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext>pptx</fileext>\n\t\t\t<cdnattachurl>3057020100044b30490201000204bf693cc002032dcfbe0204914cf8240204688ecc76042430336535653236632d396666302d346430622d393463622d3239653937663635336164380204051400050201000405004c50ba00</cdnattachurl>\n\t\t\t<aeskey>3effa7f808753e85c64ea67361266300</aeskey>\n\t\t\t<encryver>0</encryver>\n\t\t\t<overwrite_newmsgid>5370956075794921189</overwrite_newmsgid>\n\t\t\t<fileuploadtoken>v1_3mIkrPJZ4XGN3arMrDN0uEuw3pTjDh9JHxkBZ8a82Jnsuv/xx1I4h59b3pvn3wkHTq1UlsePg13Cb7xDE30i++7YEWgtl7M3fmX9Gm8hXQS9DBssPm4WJy3sACm1e1/Tix/esryzHRjXcDRu7EKtKNqHm8vVmS0NfnkYSGt0+l+hGL4MNWGayg+9GNsdGVVYHcVFqsFp2g==</fileuploadtoken>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<thumburl />\n\t\t<md5>d4d5e996e167efa2b986708ccc814fc4</md5>\n\t\t<statextstr />\n\t</appmsg>\n\t<fromusername></fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>7</version>\n\t\t<appname>微信电脑版</appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>',attachId:"@cdn_3057020100044b30490201000204bf693cc002032dcfbe0204914cf8240204688ecc76042430336535653236632d396666302d346430622d393463622d3239653937663635336164380204051400050201000405004c50ba00_3effa7f808753e85c64ea67361266300_1",cdnUrl:"3057020100044b30490201000204bf693cc002032dcfbe0204914cf8240204688ecc76042430336535653236632d396666302d346430622d393463622d3239653937663635336164380204051400050201000405004c50ba00",aesKey:"3effa7f808753e85c64ea67361266300",appId:"wx6618f1cfc6c132f8",cacheTime:1754202909524,lastUsed:1754202950839}]];function j(){try{const e="wechat_file_cache",n=localStorage.getItem(e);let a=new Map;if(n)try{const t=JSON.parse(n);a=new Map(t)}catch(t){}let r=0,c=0;L.forEach(([t,e])=>{const n=a.get(t);if(n){const r=i(s({},e),{cacheTime:Date.now(),lastUsed:n.lastUsed||Date.now()});a.set(t,r),c++}else{const n=i(s({},e),{cacheTime:Date.now(),lastUsed:Date.now()});a.set(t,n),r++}});const o=Array.from(a.entries());localStorage.setItem(e,JSON.stringify(o))}catch(t){}}function k(t,e,n){try{const r="wechat_file_cache",c=`${t}_${e}`;let o=new Map;const l=localStorage.getItem(r);if(l)try{const t=JSON.parse(l);o=new Map(t)}catch(a){}const d=i(s({},n),{cacheTime:Date.now(),lastUsed:Date.now()});o.set(c,d);const m=Array.from(o.entries());localStorage.setItem(r,JSON.stringify(m))}catch(a){}}function I(){return L.map(([t,e])=>({fileName:e.fileName,fileSize:e.fileSize,attachId:e.attachId}))}function D(){try{const t="wechat_file_cache",e=localStorage.getItem(t);if(!e)return!1;const n=JSON.parse(e),a=new Map(n);return L.every(([t])=>a.has(t))}catch(t){return!1}}function A(){try{const t="wechat_file_cache",e=localStorage.getItem(t);if(e){const t=JSON.parse(e),n=new Map(t);L.forEach(([t,e])=>{n.has(t)})}}catch(t){}}"undefined"!=typeof window&&(window.debugFileCache=A),document.documentElement.classList.remove("dark"),document.documentElement.classList.add("light"),document.documentElement.setAttribute("data-theme","light"),document.body.classList.remove("dark"),document.body.classList.add("light"),document.body.setAttribute("data-theme","light");const M=new MutationObserver(t=>{t.forEach(t=>{if("attributes"===t.type&&("class"===t.attributeName||"data-theme"===t.attributeName)){const e=t.target;e.classList.contains("dark")&&(e.classList.remove("dark"),e.classList.add("light"),e.setAttribute("data-theme","light"))}})});M.observe(document.documentElement,{attributes:!0,attributeFilter:["class","data-theme"]}),M.observe(document.body,{attributes:!0,attributeFilter:["class","data-theme"]});const J=[{path:"/",redirect:"/dashboard"},{path:"/dashboard",name:"Dashboard",component:()=>O(()=>import("./dashboard-Bevke8u2.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11]))},{path:"/login",name:"Login",component:()=>O(()=>import("./login-GQbWWj55.js"),__vite__mapDeps([12,1,2,3,4,5,6,7,8,13]))},{path:"/friends",name:"Friends",component:()=>O(()=>import("./friends-BxLvEk3H.js"),__vite__mapDeps([14,1,2,3,4,5,9,10,15,8]))}],T=b({history:g(),routes:J}),U=w(N);_(),U.use(P),U.use(T),U.mount("#app");try{j();const t=localStorage.getItem("wechat_file_cache");if(t){JSON.parse(t)}}catch(G){}export{O as _,j as a,k as b,A as d,I as g,D as i};

const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["js/components-BbzPJspU.js","js/element-plus-DAEtRXw7.js","js/vue-C-lRF7aZ.js","js/utils-XWYVm-q4.js","assets/components-CXH3rzJ9.css"])))=>i.map(i=>d[i]);
var e=(e,a,l)=>new Promise((t,s)=>{var n=e=>{try{r(l.next(e))}catch(a){s(a)}},i=e=>{try{r(l.throw(e))}catch(a){s(a)}},r=e=>e.done?t(e.value):Promise.resolve(e.value).then(n,i);r((l=l.apply(e,a)).next())});import{_ as a}from"./index-BV89XUvk.js";import{u as l,_ as t}from"./components-BbzPJspU.js";/* empty css              */import{u as s}from"./avatar-CHH9M8bf.js";import{t as n,r as i,V as r,f as c,az as u,c as d,v as o,A as f,L as v,ah as m,u as _,M as p,E as g,x as h,J as y,K as w,H as x,G as k}from"./vue-C-lRF7aZ.js";import{E as b,c as A,k as V,a as C,M as S,m as j,s as D,z as U,n as $,o as z,P as T,Q as F,a4 as N,R as P,J as E,K as I,C as L,x as M,ac as G,G as O}from"./element-plus-DAEtRXw7.js";import"./utils-XWYVm-q4.js";const R={class:"friends-container"},H={class:"account-sidebar"},J={class:"account-list"},K=["onClick"],q={class:"account-info"},Q={class:"nickname"},B={class:"status"},W={class:"sidebar-footer"},X={class:"main-content"},Y={class:"content-header"},Z={class:"header-actions"},ee={class:"friends-list-panel"},ae={class:"list-header"},le={class:"friend-stats"},te={class:"friends-grid"},se={class:"friend-info"},ne={class:"friend-name"},ie={class:"friend-wxid"},re={class:"friend-meta"},ce={key:0,class:"signature"},ue={class:"friend-actions"},de={class:"search-panel"},oe={key:0,class:"search-results"},fe={class:"result-list"},ve={class:"contact-info"},me={class:"contact-name"},_e={class:"contact-wxid"},pe={class:"contact-signature"},ge={class:"batch-panel"},he=t(n({__name:"friends",setup(t){const n=3,he=u(),ye=l(),we=s(),xe=i("list"),ke=i("");i([]);const be=r({keyword:"",searchType:"wxid"}),Ae=r({targets:"",message:"你好，我想加你为好友"}),Ve=i([]),Ce=i(!1);c(()=>{ye.isLoggedIn?je():he.push("/login")});const Se=d(()=>{if(!ye.currentAccount)return[];const e=we.currentFriends(ye.currentAccount.wxid);return ke.value?e.filter(e=>e.nickname.includes(ke.value)||e.wxid.includes(ke.value)||e.remark&&e.remark.includes(ke.value)):e}),je=()=>e(null,null,function*(){if(ye.currentAccount)try{yield we.loadFriends(ye.currentAccount.wxid,!0),b.success("好友列表加载完成")}catch(e){b.error("加载好友列表失败")}}),De=()=>e(null,null,function*(){var e,a,l,t,s,n;if(be.keyword.trim())if(ye.currentAccount){Ce.value=!0,Ve.value=[];try{const i=yield we.searchContact(ye.currentAccount.wxid,be.keyword.trim());if(i.Success&&i.Data){const r=(null==(a=null==(e=i.Data.UserName)?void 0:e.string)?void 0:a.includes("@stranger"))||!1;Ve.value=[{wxid:r?(null==(l=i.Data.Pyinitial)?void 0:l.string)||"":(null==(t=i.Data.UserName)?void 0:t.string)||"",nickname:(null==(s=i.Data.NickName)?void 0:s.string)||"",avatar:i.Data.SmallHeadImgUrl||"",signature:i.Data.Signature||"",sex:i.Data.Sex||0,v1:(null==(n=i.Data.UserName)?void 0:n.string)||"",v2:i.Data.AntispamTicket||""}],b.success("搜索完成")}else b.warning(i.Message||"未找到相关用户")}catch(i){b.error("搜索失败")}finally{Ce.value=!1}}else b.error("请先选择账号");else b.warning("请输入搜索关键词")}),Ue=()=>e(null,null,function*(){if(!Ae.targets.trim())return void b.warning("请输入要添加的好友列表");if(!ye.currentAccount)return void b.error("请先选择账号");const e=Ae.targets.split("\n").map(e=>e.trim()).filter(e=>e).map(e=>({identifier:e,message:Ae.message}));if(0!==e.length)try{const a=yield we.batchAddFriends(ye.currentAccount.wxid,e),l=a.filter(e=>e.success).length,t=a.length-l;b.success(`批量添加完成：成功 ${l} 个，失败 ${t} 个`);const s=a.filter(e=>!e.success);if(s.length>0){const e=s.map(e=>`${e.target}: ${e.message}`).join("\n");E.alert(e,"失败详情",{type:"warning"})}}catch(a){b.error("批量添加失败")}else b.warning("请输入有效的好友标识")}),$e=()=>{he.push("/dashboard")},ze=e=>{switch(e){case 1:return"男";case 2:return"女";default:return"未知"}},Te=e=>null==e?"":"string"==typeof e?e:"number"==typeof e?String(e):"object"==typeof e?0===Object.keys(e).length?"":void 0!==e.string&&null!==e.string&&""!==e.string?String(e.string):void 0!==e.value&&null!==e.value&&""!==e.value?String(e.value):void 0!==e.text&&null!==e.text&&""!==e.text?String(e.text):"":String(e);return(l,t)=>{const s=C,i=L,r=A,c=D,u=j,d=F,Fe=T,Ne=z,Pe=$,Ee=P,Ie=V;return h(),o("div",R,[f("div",H,[t[7]||(t[7]=f("div",{class:"sidebar-header"},[f("h3",null,"选择账号")],-1)),f("div",J,[(h(!0),o(v,null,m(_(ye).accounts,a=>{var l;return h(),o("div",{key:a.wxid,class:k(["account-item",{active:(null==(l=_(ye).currentAccount)?void 0:l.wxid)===a.wxid}]),onClick:l=>(a=>e(null,null,function*(){var e;(null==(e=ye.currentAccount)?void 0:e.wxid)!==a.wxid&&(friends.value=[],filteredFriends.value=[],ke.value="",b.info(`已切换到账号：${a.nickname}，正在获取通讯录...`)),ye.setCurrentAccount(a.wxid),yield je()}))(a)},[p(i,{src:a.avatar,size:32},{default:g(()=>[p(s,null,{default:g(()=>[p(_(I))]),_:1})]),_:2},1032,["src"]),f("div",q,[f("div",Q,w(a.nickname),1),f("div",B,w("online"===a.status?"在线":"离线"),1)])],10,K)}),128))]),f("div",W,[p(r,{onClick:$e,link:""},{default:g(()=>t[6]||(t[6]=[y(" 返回聊天 ",-1)])),_:1,__:[6]})])]),f("div",X,[f("div",Y,[t[9]||(t[9]=f("h2",null,"好友管理",-1)),f("div",Z,[p(r,{onClick:je,loading:_(we).isLoading},{default:g(()=>[p(s,null,{default:g(()=>[p(_(S))]),_:1}),t[8]||(t[8]=y(" 刷新 ",-1))]),_:1,__:[8]},8,["loading"])])]),p(Ie,{modelValue:xe.value,"onUpdate:modelValue":t[5]||(t[5]=e=>xe.value=e),class:"content-tabs"},{default:g(()=>[p(u,{label:"好友列表",name:"list"},{default:g(()=>[f("div",ee,[f("div",ae,[p(c,{modelValue:ke.value,"onUpdate:modelValue":t[0]||(t[0]=e=>ke.value=e),placeholder:"搜索好友...",style:{width:"300px"}},{prefix:g(()=>[p(s,null,{default:g(()=>[p(_(U))]),_:1})]),_:1},8,["modelValue"]),f("div",le," 共 "+w(Se.value.length)+" 个好友 ",1)]),f("div",te,[(h(!0),o(v,null,m(Se.value,l=>(h(),o("div",{key:l.wxid,class:"friend-card"},[p(i,{src:l.avatar,size:60},{default:g(()=>[p(s,null,{default:g(()=>[p(_(M))]),_:1})]),_:2},1032,["src"]),f("div",se,[f("div",ne,w(Te(l.remark)||Te(l.nickname)),1),f("div",ie,w(Te(l.wxid)),1),f("div",re,[y(w(ze(l.sex))+" ",1),Te(l.signature)?(h(),o("span",ce," | "+w(Te(l.signature)),1)):x("",!0)])]),f("div",ue,[p(r,{size:"small",onClick:t=>(l=>e(null,null,function*(){if(ye.currentAccount)try{const{useChatStore:t}=yield a(()=>e(null,null,function*(){const{useChatStore:e}=yield import("./components-BbzPJspU.js").then(e=>e.g);return{useChatStore:e}}),__vite__mapDeps([0,1,2,3,4])),s=t(),n=s.createOrGetSession(l);s.setCurrentSession(n.id),yield he.push("/dashboard?tab=chat"),b.success(`已开始与 ${l.nickname} 的聊天`)}catch(t){b.error("开始聊天失败，请重试")}else b.error("请先选择账号")}))(l)},{default:g(()=>[p(s,null,{default:g(()=>[p(_(G))]),_:1}),t[10]||(t[10]=y(" 聊天 ",-1))]),_:2,__:[10]},1032,["onClick"]),p(r,{size:"small",type:"danger",onClick:a=>(a=>e(null,null,function*(){try{yield E.confirm(`确定要删除好友 ${a.nickname} 吗？`,"确认删除",{type:"warning"}),b.success("删除成功"),je()}catch(e){}}))(l)},{default:g(()=>[p(s,null,{default:g(()=>[p(_(O))]),_:1}),t[11]||(t[11]=y(" 删除 ",-1))]),_:2,__:[11]},1032,["onClick"])])]))),128))])])]),_:1}),p(u,{label:"搜索添加",name:"search"},{default:g(()=>[f("div",de,[p(Pe,{model:be,"label-width":"100px"},{default:g(()=>[p(Ne,{label:"搜索类型"},{default:g(()=>[p(Fe,{modelValue:be.searchType,"onUpdate:modelValue":t[1]||(t[1]=e=>be.searchType=e)},{default:g(()=>[p(d,{value:"wxid"},{default:g(()=>t[12]||(t[12]=[y("微信号",-1)])),_:1,__:[12]}),p(d,{value:"phone"},{default:g(()=>t[13]||(t[13]=[y("手机号",-1)])),_:1,__:[13]})]),_:1},8,["modelValue"])]),_:1}),p(Ne,{label:"关键词"},{default:g(()=>[p(c,{modelValue:be.keyword,"onUpdate:modelValue":t[2]||(t[2]=e=>be.keyword=e),placeholder:"wxid"===be.searchType?"输入微信号":"输入手机号",style:{width:"300px"}},{append:g(()=>[p(r,{onClick:De,loading:Ce.value},{default:g(()=>[p(s,null,{default:g(()=>[p(_(U))]),_:1}),t[14]||(t[14]=y(" 搜索 ",-1))]),_:1,__:[14]},8,["loading"])]),_:1},8,["modelValue","placeholder"])]),_:1})]),_:1},8,["model"]),Ve.value.length>0?(h(),o("div",oe,[t[16]||(t[16]=f("h4",null,"搜索结果",-1)),f("div",fe,[(h(!0),o(v,null,m(Ve.value,a=>(h(),o("div",{key:a.wxid,class:"result-item"},[p(i,{src:a.avatar,size:50},{default:g(()=>[p(s,null,{default:g(()=>[p(_(M))]),_:1})]),_:2},1032,["src"]),f("div",ve,[f("div",me,w(Te(a.nickname)),1),f("div",_e,w(Te(a.wxid)),1),f("div",pe,w(Te(a.signature)||"暂无签名"),1)]),p(r,{type:"primary",onClick:l=>((a,l)=>e(null,null,function*(){if(ye.currentAccount)if(a.v1&&a.v2)try{const e=l||`你好，我是${ye.currentAccount.nickname}`,t=yield we.sendFriendRequest(ye.currentAccount.wxid,a.v1,a.v2,e,n);t.Success?b.success("好友请求发送成功"):b.error(t.Message||"发送好友请求失败")}catch(e){b.error(e.message||"发送好友请求失败")}else b.error("联系人信息不完整，请重新搜索");else b.error("请先选择账号")}))(a)},{default:g(()=>[p(s,null,{default:g(()=>[p(_(N))]),_:1}),t[15]||(t[15]=y(" 添加好友 ",-1))]),_:2,__:[15]},1032,["onClick"])]))),128))])])):x("",!0)])]),_:1}),p(u,{label:"批量添加",name:"batch"},{default:g(()=>[f("div",ge,[p(Pe,{model:Ae,"label-width":"100px"},{default:g(()=>[p(Ne,{label:"验证消息"},{default:g(()=>[p(c,{modelValue:Ae.message,"onUpdate:modelValue":t[3]||(t[3]=e=>Ae.message=e),placeholder:"输入好友验证消息",style:{width:"400px"}},null,8,["modelValue"])]),_:1}),p(Ne,{label:"好友列表"},{default:g(()=>[p(c,{modelValue:Ae.targets,"onUpdate:modelValue":t[4]||(t[4]=e=>Ae.targets=e),type:"textarea",rows:10,placeholder:"每行一个微信号或手机号，例如：\nwxid_123456\n13800138000\nwxid_abcdef",style:{width:"400px"}},null,8,["modelValue"])]),_:1}),p(Ne,null,{default:g(()=>[p(r,{type:"primary",onClick:Ue,loading:_(we).isAdding},{default:g(()=>[p(s,null,{default:g(()=>[p(_(N))]),_:1}),t[17]||(t[17]=y(" 开始批量添加 ",-1))]),_:1,__:[17]},8,["loading"])]),_:1})]),_:1},8,["model"]),p(Ee,{title:"批量添加说明",type:"info",closable:!1,"show-icon":""},{default:g(()=>t[18]||(t[18]=[f("p",null,"1. 每行输入一个微信号或手机号",-1),f("p",null,"2. 系统会自动搜索并发送好友请求",-1),f("p",null,"3. 为避免频率限制，每个请求间隔2秒",-1),f("p",null,"4. 建议单次添加不超过50个好友",-1)])),_:1,__:[18]})])]),_:1})]),_:1},8,["modelValue"])])])}}}),[["__scopeId","data-v-f3510df0"]]);export{he as default};

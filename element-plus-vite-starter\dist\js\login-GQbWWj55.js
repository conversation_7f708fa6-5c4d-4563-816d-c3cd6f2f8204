var e=(e,t,n)=>new Promise((r,o)=>{var a=e=>{try{i(n.next(e))}catch(t){o(t)}},l=e=>{try{i(n.throw(e))}catch(t){o(t)}},i=e=>e.done?r(e.value):Promise.resolve(e.value).then(a,l);i((n=n.apply(e,t)).next())});import{u as t,l as n,_ as r}from"./components-BbzPJspU.js";/* empty css              */import{p as o,P as a,g as l}from"./ProxyManagement-B-RgoOEE.js";import{t as i,r as u,V as s,f as c,v as d,A as f,M as h,E as g,x as p,L as m,ah as v,J as y,H as w,D as P,u as b,az as x}from"./vue-C-lRF7aZ.js";import{a9 as E,c as _,k as C,i as I,m as A,n as N,o as T,q as U,s as M,t as V,u as B,P as k,Q as R,r as D,R as L,a as S,M as z,S as H,w as q,p as F,E as j}from"./element-plus-DAEtRXw7.js";import"./utils-XWYVm-q4.js";var J,K,Y={};var $,O={},Q={};function W(){if($)return Q;let e;$=1;const t=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];return Q.getSymbolSize=function(e){if(!e)throw new Error('"version" cannot be null or undefined');if(e<1||e>40)throw new Error('"version" should be in range from 1 to 40');return 4*e+17},Q.getSymbolTotalCodewords=function(e){return t[e]},Q.getBCHDigit=function(e){let t=0;for(;0!==e;)t++,e>>>=1;return t},Q.setToSJISFunction=function(t){if("function"!=typeof t)throw new Error('"toSJISFunc" is not a valid function.');e=t},Q.isKanjiModeEnabled=function(){return void 0!==e},Q.toSJIS=function(t){return e(t)},Q}var X,Z,G,ee,te,ne={};function re(){return X||(X=1,(e=ne).L={bit:1},e.M={bit:0},e.Q={bit:3},e.H={bit:2},e.isValid=function(e){return e&&void 0!==e.bit&&e.bit>=0&&e.bit<4},e.from=function(t,n){if(e.isValid(t))return t;try{return function(t){if("string"!=typeof t)throw new Error("Param is not a string");switch(t.toLowerCase()){case"l":case"low":return e.L;case"m":case"medium":return e.M;case"q":case"quartile":return e.Q;case"h":case"high":return e.H;default:throw new Error("Unknown EC Level: "+t)}}(t)}catch(r){return n}}),ne;var e}var oe,ae={};var le,ie={};var ue,se={};var ce,de={};function fe(){if(ce)return de;ce=1;const e=re(),t=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],n=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];return de.getBlocksCount=function(n,r){switch(r){case e.L:return t[4*(n-1)+0];case e.M:return t[4*(n-1)+1];case e.Q:return t[4*(n-1)+2];case e.H:return t[4*(n-1)+3];default:return}},de.getTotalCodewordsCount=function(t,r){switch(r){case e.L:return n[4*(t-1)+0];case e.M:return n[4*(t-1)+1];case e.Q:return n[4*(t-1)+2];case e.H:return n[4*(t-1)+3];default:return}},de}var he,ge,pe,me,ve={},ye={};function we(){return ge||(ge=1,function(e){const t=function(){if(he)return ye;he=1;const e=new Uint8Array(512),t=new Uint8Array(256);return function(){let n=1;for(let r=0;r<255;r++)e[r]=n,t[n]=r,n<<=1,256&n&&(n^=285);for(let t=255;t<512;t++)e[t]=e[t-255]}(),ye.log=function(e){if(e<1)throw new Error("log("+e+")");return t[e]},ye.exp=function(t){return e[t]},ye.mul=function(n,r){return 0===n||0===r?0:e[t[n]+t[r]]},ye}();e.mul=function(e,n){const r=new Uint8Array(e.length+n.length-1);for(let o=0;o<e.length;o++)for(let a=0;a<n.length;a++)r[o+a]^=t.mul(e[o],n[a]);return r},e.mod=function(e,n){let r=new Uint8Array(e);for(;r.length-n.length>=0;){const e=r[0];for(let a=0;a<n.length;a++)r[a]^=t.mul(n[a],e);let o=0;for(;o<r.length&&0===r[o];)o++;r=r.slice(o)}return r},e.generateECPolynomial=function(n){let r=new Uint8Array([1]);for(let o=0;o<n;o++)r=e.mul(r,new Uint8Array([1,t.exp(o)]));return r}}(ve)),ve}var Pe,be={},xe={},Ee={};function _e(){return Pe||(Pe=1,Ee.isValid=function(e){return!isNaN(e)&&e>=1&&e<=40}),Ee}var Ce,Ie,Ae,Ne={};function Te(){if(Ce)return Ne;Ce=1;const e="[0-9]+";let t="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";t=t.replace(/u/g,"\\u");const n="(?:(?![A-Z0-9 $%*+\\-./:]|"+t+")(?:.|[\r\n]))+";Ne.KANJI=new RegExp(t,"g"),Ne.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),Ne.BYTE=new RegExp(n,"g"),Ne.NUMERIC=new RegExp(e,"g"),Ne.ALPHANUMERIC=new RegExp("[A-Z $%*+\\-./:]+","g");const r=new RegExp("^"+t+"$"),o=new RegExp("^"+e+"$"),a=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");return Ne.testKanji=function(e){return r.test(e)},Ne.testNumeric=function(e){return o.test(e)},Ne.testAlphanumeric=function(e){return a.test(e)},Ne}function Ue(){return Ie||(Ie=1,function(e){const t=_e(),n=Te();e.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},e.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},e.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},e.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},e.MIXED={bit:-1},e.getCharCountIndicator=function(e,n){if(!e.ccBits)throw new Error("Invalid mode: "+e);if(!t.isValid(n))throw new Error("Invalid version: "+n);return n>=1&&n<10?e.ccBits[0]:n<27?e.ccBits[1]:e.ccBits[2]},e.getBestModeForData=function(t){return n.testNumeric(t)?e.NUMERIC:n.testAlphanumeric(t)?e.ALPHANUMERIC:n.testKanji(t)?e.KANJI:e.BYTE},e.toString=function(e){if(e&&e.id)return e.id;throw new Error("Invalid mode")},e.isValid=function(e){return e&&e.bit&&e.ccBits},e.from=function(t,n){if(e.isValid(t))return t;try{return function(t){if("string"!=typeof t)throw new Error("Param is not a string");switch(t.toLowerCase()){case"numeric":return e.NUMERIC;case"alphanumeric":return e.ALPHANUMERIC;case"kanji":return e.KANJI;case"byte":return e.BYTE;default:throw new Error("Unknown mode: "+t)}}(t)}catch(r){return n}}}(xe)),xe}function Me(){return Ae||(Ae=1,function(e){const t=W(),n=fe(),r=re(),o=Ue(),a=_e(),l=t.getBCHDigit(7973);function i(e,t){return o.getCharCountIndicator(e,t)+4}function u(e,t){let n=0;return e.forEach(function(e){const r=i(e.mode,t);n+=r+e.getBitsLength()}),n}e.from=function(e,t){return a.isValid(e)?parseInt(e,10):t},e.getCapacity=function(e,r,l){if(!a.isValid(e))throw new Error("Invalid QR Code version");void 0===l&&(l=o.BYTE);const u=8*(t.getSymbolTotalCodewords(e)-n.getTotalCodewordsCount(e,r));if(l===o.MIXED)return u;const s=u-i(l,e);switch(l){case o.NUMERIC:return Math.floor(s/10*3);case o.ALPHANUMERIC:return Math.floor(s/11*2);case o.KANJI:return Math.floor(s/13);case o.BYTE:default:return Math.floor(s/8)}},e.getBestVersionForData=function(t,n){let a;const l=r.from(n,r.M);if(Array.isArray(t)){if(t.length>1)return function(t,n){for(let r=1;r<=40;r++)if(u(t,r)<=e.getCapacity(r,n,o.MIXED))return r}(t,l);if(0===t.length)return 1;a=t[0]}else a=t;return function(t,n,r){for(let o=1;o<=40;o++)if(n<=e.getCapacity(o,r,t))return o}(a.mode,a.getLength(),l)},e.getEncodedBits=function(e){if(!a.isValid(e)||e<7)throw new Error("Invalid QR Code version");let n=e<<12;for(;t.getBCHDigit(n)-l>=0;)n^=7973<<t.getBCHDigit(n)-l;return e<<12|n}}(be)),be}var Ve,Be={};var ke,Re,De,Le,Se,ze,He,qe,Fe={};var je,Je,Ke,Ye={exports:{}};function $e(){return Je||(Je=1,function(e){const t=Ue(),n=function(){if(Re)return ke;Re=1;const e=Ue();function t(t){this.mode=e.NUMERIC,this.data=t.toString()}return t.getBitsLength=function(e){return 10*Math.floor(e/3)+(e%3?e%3*3+1:0)},t.prototype.getLength=function(){return this.data.length},t.prototype.getBitsLength=function(){return t.getBitsLength(this.data.length)},t.prototype.write=function(e){let t,n,r;for(t=0;t+3<=this.data.length;t+=3)n=this.data.substr(t,3),r=parseInt(n,10),e.put(r,10);const o=this.data.length-t;o>0&&(n=this.data.substr(t),r=parseInt(n,10),e.put(r,3*o+1))},ke=t}(),r=function(){if(Le)return De;Le=1;const e=Ue(),t=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function n(t){this.mode=e.ALPHANUMERIC,this.data=t}return n.getBitsLength=function(e){return 11*Math.floor(e/2)+e%2*6},n.prototype.getLength=function(){return this.data.length},n.prototype.getBitsLength=function(){return n.getBitsLength(this.data.length)},n.prototype.write=function(e){let n;for(n=0;n+2<=this.data.length;n+=2){let r=45*t.indexOf(this.data[n]);r+=t.indexOf(this.data[n+1]),e.put(r,11)}this.data.length%2&&e.put(t.indexOf(this.data[n]),6)},De=n}(),o=function(){if(ze)return Se;ze=1;const e=Ue();function t(t){this.mode=e.BYTE,this.data="string"==typeof t?(new TextEncoder).encode(t):new Uint8Array(t)}return t.getBitsLength=function(e){return 8*e},t.prototype.getLength=function(){return this.data.length},t.prototype.getBitsLength=function(){return t.getBitsLength(this.data.length)},t.prototype.write=function(e){for(let t=0,n=this.data.length;t<n;t++)e.put(this.data[t],8)},Se=t}(),a=function(){if(qe)return He;qe=1;const e=Ue(),t=W();function n(t){this.mode=e.KANJI,this.data=t}return n.getBitsLength=function(e){return 13*e},n.prototype.getLength=function(){return this.data.length},n.prototype.getBitsLength=function(){return n.getBitsLength(this.data.length)},n.prototype.write=function(e){let n;for(n=0;n<this.data.length;n++){let r=t.toSJIS(this.data[n]);if(r>=33088&&r<=40956)r-=33088;else{if(!(r>=57408&&r<=60351))throw new Error("Invalid SJIS character: "+this.data[n]+"\nMake sure your charset is UTF-8");r-=49472}r=192*(r>>>8&255)+(255&r),e.put(r,13)}},He=n}(),l=Te(),i=W(),u=function(){return je?Ye.exports:(je=1,Ye.exports=e={single_source_shortest_paths:function(t,n,r){var o={},a={};a[n]=0;var l,i,u,s,c,d,f,h=e.PriorityQueue.make();for(h.push(n,0);!h.empty();)for(u in i=(l=h.pop()).value,s=l.cost,c=t[i]||{})c.hasOwnProperty(u)&&(d=s+c[u],f=a[u],(void 0===a[u]||f>d)&&(a[u]=d,h.push(u,d),o[u]=i));if(void 0!==r&&void 0===a[r]){var g=["Could not find a path from ",n," to ",r,"."].join("");throw new Error(g)}return o},extract_shortest_path_from_predecessor_list:function(e,t){for(var n=[],r=t;r;)n.push(r),e[r],r=e[r];return n.reverse(),n},find_path:function(t,n,r){var o=e.single_source_shortest_paths(t,n,r);return e.extract_shortest_path_from_predecessor_list(o,r)},PriorityQueue:{make:function(t){var n,r=e.PriorityQueue,o={};for(n in t=t||{},r)r.hasOwnProperty(n)&&(o[n]=r[n]);return o.queue=[],o.sorter=t.sorter||r.default_sorter,o},default_sorter:function(e,t){return e.cost-t.cost},push:function(e,t){var n={value:e,cost:t};this.queue.push(n),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}});var e}();function s(e){return unescape(encodeURIComponent(e)).length}function c(e,t,n){const r=[];let o;for(;null!==(o=e.exec(n));)r.push({data:o[0],index:o.index,mode:t,length:o[0].length});return r}function d(e){const n=c(l.NUMERIC,t.NUMERIC,e),r=c(l.ALPHANUMERIC,t.ALPHANUMERIC,e);let o,a;i.isKanjiModeEnabled()?(o=c(l.BYTE,t.BYTE,e),a=c(l.KANJI,t.KANJI,e)):(o=c(l.BYTE_KANJI,t.BYTE,e),a=[]);return n.concat(r,o,a).sort(function(e,t){return e.index-t.index}).map(function(e){return{data:e.data,mode:e.mode,length:e.length}})}function f(e,l){switch(l){case t.NUMERIC:return n.getBitsLength(e);case t.ALPHANUMERIC:return r.getBitsLength(e);case t.KANJI:return a.getBitsLength(e);case t.BYTE:return o.getBitsLength(e)}}function h(e,l){let u;const s=t.getBestModeForData(e);if(u=t.from(l,s),u!==t.BYTE&&u.bit<s.bit)throw new Error('"'+e+'" cannot be encoded with mode '+t.toString(u)+".\n Suggested mode is: "+t.toString(s));switch(u!==t.KANJI||i.isKanjiModeEnabled()||(u=t.BYTE),u){case t.NUMERIC:return new n(e);case t.ALPHANUMERIC:return new r(e);case t.KANJI:return new a(e);case t.BYTE:return new o(e)}}e.fromArray=function(e){return e.reduce(function(e,t){return"string"==typeof t?e.push(h(t,null)):t.data&&e.push(h(t.data,t.mode)),e},[])},e.fromString=function(n,r){const o=function(e){const n=[];for(let r=0;r<e.length;r++){const o=e[r];switch(o.mode){case t.NUMERIC:n.push([o,{data:o.data,mode:t.ALPHANUMERIC,length:o.length},{data:o.data,mode:t.BYTE,length:o.length}]);break;case t.ALPHANUMERIC:n.push([o,{data:o.data,mode:t.BYTE,length:o.length}]);break;case t.KANJI:n.push([o,{data:o.data,mode:t.BYTE,length:s(o.data)}]);break;case t.BYTE:n.push([{data:o.data,mode:t.BYTE,length:s(o.data)}])}}return n}(d(n,i.isKanjiModeEnabled())),a=function(e,n){const r={},o={start:{}};let a=["start"];for(let l=0;l<e.length;l++){const i=e[l],u=[];for(let e=0;e<i.length;e++){const s=i[e],c=""+l+e;u.push(c),r[c]={node:s,lastCount:0},o[c]={};for(let e=0;e<a.length;e++){const l=a[e];r[l]&&r[l].node.mode===s.mode?(o[l][c]=f(r[l].lastCount+s.length,s.mode)-f(r[l].lastCount,s.mode),r[l].lastCount+=s.length):(r[l]&&(r[l].lastCount=s.length),o[l][c]=f(s.length,s.mode)+4+t.getCharCountIndicator(s.mode,n))}}a=u}for(let t=0;t<a.length;t++)o[a[t]].end=0;return{map:o,table:r}}(o,r),l=u.find_path(a.map,"start","end"),c=[];for(let e=1;e<l.length-1;e++)c.push(a.table[l[e]].node);return e.fromArray(function(e){return e.reduce(function(e,t){const n=e.length-1>=0?e[e.length-1]:null;return n&&n.mode===t.mode?(e[e.length-1].data+=t.data,e):(e.push(t),e)},[])}(c))},e.rawSplit=function(t){return e.fromArray(d(t,i.isKanjiModeEnabled()))}}(Fe)),Fe}function Oe(){if(Ke)return O;Ke=1;const e=W(),t=re(),n=function(){if(G)return Z;function e(){this.buffer=[],this.length=0}return G=1,e.prototype={get:function(e){const t=Math.floor(e/8);return 1==(this.buffer[t]>>>7-e%8&1)},put:function(e,t){for(let n=0;n<t;n++)this.putBit(1==(e>>>t-n-1&1))},getLengthInBits:function(){return this.length},putBit:function(e){const t=Math.floor(this.length/8);this.buffer.length<=t&&this.buffer.push(0),e&&(this.buffer[t]|=128>>>this.length%8),this.length++}},Z=e}(),r=function(){if(te)return ee;function e(e){if(!e||e<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=e,this.data=new Uint8Array(e*e),this.reservedBit=new Uint8Array(e*e)}return te=1,e.prototype.set=function(e,t,n,r){const o=e*this.size+t;this.data[o]=n,r&&(this.reservedBit[o]=!0)},e.prototype.get=function(e,t){return this.data[e*this.size+t]},e.prototype.xor=function(e,t,n){this.data[e*this.size+t]^=n},e.prototype.isReserved=function(e,t){return this.reservedBit[e*this.size+t]},ee=e}(),o=(oe||(oe=1,function(e){const t=W().getSymbolSize;e.getRowColCoords=function(e){if(1===e)return[];const n=Math.floor(e/7)+2,r=t(e),o=145===r?26:2*Math.ceil((r-13)/(2*n-2)),a=[r-7];for(let t=1;t<n-1;t++)a[t]=a[t-1]-o;return a.push(6),a.reverse()},e.getPositions=function(t){const n=[],r=e.getRowColCoords(t),o=r.length;for(let e=0;e<o;e++)for(let t=0;t<o;t++)0===e&&0===t||0===e&&t===o-1||e===o-1&&0===t||n.push([r[e],r[t]]);return n}}(ae)),ae),a=function(){if(le)return ie;le=1;const e=W().getSymbolSize;return ie.getPositions=function(t){const n=e(t);return[[0,0],[n-7,0],[0,n-7]]},ie}(),l=(ue||(ue=1,function(e){e.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const t=3,n=3,r=40,o=10;function a(t,n,r){switch(t){case e.Patterns.PATTERN000:return(n+r)%2==0;case e.Patterns.PATTERN001:return n%2==0;case e.Patterns.PATTERN010:return r%3==0;case e.Patterns.PATTERN011:return(n+r)%3==0;case e.Patterns.PATTERN100:return(Math.floor(n/2)+Math.floor(r/3))%2==0;case e.Patterns.PATTERN101:return n*r%2+n*r%3==0;case e.Patterns.PATTERN110:return(n*r%2+n*r%3)%2==0;case e.Patterns.PATTERN111:return(n*r%3+(n+r)%2)%2==0;default:throw new Error("bad maskPattern:"+t)}}e.isValid=function(e){return null!=e&&""!==e&&!isNaN(e)&&e>=0&&e<=7},e.from=function(t){return e.isValid(t)?parseInt(t,10):void 0},e.getPenaltyN1=function(e){const n=e.size;let r=0,o=0,a=0,l=null,i=null;for(let u=0;u<n;u++){o=a=0,l=i=null;for(let s=0;s<n;s++){let n=e.get(u,s);n===l?o++:(o>=5&&(r+=t+(o-5)),l=n,o=1),n=e.get(s,u),n===i?a++:(a>=5&&(r+=t+(a-5)),i=n,a=1)}o>=5&&(r+=t+(o-5)),a>=5&&(r+=t+(a-5))}return r},e.getPenaltyN2=function(e){const t=e.size;let r=0;for(let n=0;n<t-1;n++)for(let o=0;o<t-1;o++){const t=e.get(n,o)+e.get(n,o+1)+e.get(n+1,o)+e.get(n+1,o+1);4!==t&&0!==t||r++}return r*n},e.getPenaltyN3=function(e){const t=e.size;let n=0,o=0,a=0;for(let r=0;r<t;r++){o=a=0;for(let l=0;l<t;l++)o=o<<1&2047|e.get(r,l),l>=10&&(1488===o||93===o)&&n++,a=a<<1&2047|e.get(l,r),l>=10&&(1488===a||93===a)&&n++}return n*r},e.getPenaltyN4=function(e){let t=0;const n=e.data.length;for(let r=0;r<n;r++)t+=e.data[r];return Math.abs(Math.ceil(100*t/n/5)-10)*o},e.applyMask=function(e,t){const n=t.size;for(let r=0;r<n;r++)for(let o=0;o<n;o++)t.isReserved(o,r)||t.xor(o,r,a(e,o,r))},e.getBestMask=function(t,n){const r=Object.keys(e.Patterns).length;let o=0,a=1/0;for(let l=0;l<r;l++){n(l),e.applyMask(l,t);const r=e.getPenaltyN1(t)+e.getPenaltyN2(t)+e.getPenaltyN3(t)+e.getPenaltyN4(t);e.applyMask(l,t),r<a&&(a=r,o=l)}return o}}(se)),se),i=fe(),u=function(){if(me)return pe;me=1;const e=we();function t(e){this.genPoly=void 0,this.degree=e,this.degree&&this.initialize(this.degree)}return t.prototype.initialize=function(t){this.degree=t,this.genPoly=e.generateECPolynomial(this.degree)},t.prototype.encode=function(t){if(!this.genPoly)throw new Error("Encoder not initialized");const n=new Uint8Array(t.length+this.degree);n.set(t);const r=e.mod(n,this.genPoly),o=this.degree-r.length;if(o>0){const e=new Uint8Array(this.degree);return e.set(r,o),e}return r},pe=t}(),s=Me(),c=function(){if(Ve)return Be;Ve=1;const e=W(),t=e.getBCHDigit(1335);return Be.getEncodedBits=function(n,r){const o=n.bit<<3|r;let a=o<<10;for(;e.getBCHDigit(a)-t>=0;)a^=1335<<e.getBCHDigit(a)-t;return 21522^(o<<10|a)},Be}(),d=Ue(),f=$e();function h(e,t,n){const r=e.size,o=c.getEncodedBits(t,n);let a,l;for(a=0;a<15;a++)l=1==(o>>a&1),a<6?e.set(a,8,l,!0):a<8?e.set(a+1,8,l,!0):e.set(r-15+a,8,l,!0),a<8?e.set(8,r-a-1,l,!0):a<9?e.set(8,15-a-1+1,l,!0):e.set(8,15-a-1,l,!0);e.set(r-8,8,1,!0)}function g(t,r,o){const a=new n;o.forEach(function(e){a.put(e.mode.bit,4),a.put(e.getLength(),d.getCharCountIndicator(e.mode,t)),e.write(a)});const l=8*(e.getSymbolTotalCodewords(t)-i.getTotalCodewordsCount(t,r));for(a.getLengthInBits()+4<=l&&a.put(0,4);a.getLengthInBits()%8!=0;)a.putBit(0);const s=(l-a.getLengthInBits())/8;for(let e=0;e<s;e++)a.put(e%2?17:236,8);return function(t,n,r){const o=e.getSymbolTotalCodewords(n),a=i.getTotalCodewordsCount(n,r),l=o-a,s=i.getBlocksCount(n,r),c=o%s,d=s-c,f=Math.floor(o/s),h=Math.floor(l/s),g=h+1,p=f-h,m=new u(p);let v=0;const y=new Array(s),w=new Array(s);let P=0;const b=new Uint8Array(t.buffer);for(let e=0;e<s;e++){const t=e<d?h:g;y[e]=b.slice(v,v+t),w[e]=m.encode(y[e]),v+=t,P=Math.max(P,t)}const x=new Uint8Array(o);let E,_,C=0;for(E=0;E<P;E++)for(_=0;_<s;_++)E<y[_].length&&(x[C++]=y[_][E]);for(E=0;E<p;E++)for(_=0;_<s;_++)x[C++]=w[_][E];return x}(a,t,r)}function p(t,n,i,u){let c;if(Array.isArray(t))c=f.fromArray(t);else{if("string"!=typeof t)throw new Error("Invalid data");{let e=n;if(!e){const n=f.rawSplit(t);e=s.getBestVersionForData(n,i)}c=f.fromString(t,e||40)}}const d=s.getBestVersionForData(c,i);if(!d)throw new Error("The amount of data is too big to be stored in a QR Code");if(n){if(n<d)throw new Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+d+".\n")}else n=d;const p=g(n,i,c),m=e.getSymbolSize(n),v=new r(m);return function(e,t){const n=e.size,r=a.getPositions(t);for(let o=0;o<r.length;o++){const t=r[o][0],a=r[o][1];for(let r=-1;r<=7;r++)if(!(t+r<=-1||n<=t+r))for(let o=-1;o<=7;o++)a+o<=-1||n<=a+o||(r>=0&&r<=6&&(0===o||6===o)||o>=0&&o<=6&&(0===r||6===r)||r>=2&&r<=4&&o>=2&&o<=4?e.set(t+r,a+o,!0,!0):e.set(t+r,a+o,!1,!0))}}(v,n),function(e){const t=e.size;for(let n=8;n<t-8;n++){const t=n%2==0;e.set(n,6,t,!0),e.set(6,n,t,!0)}}(v),function(e,t){const n=o.getPositions(t);for(let r=0;r<n.length;r++){const t=n[r][0],o=n[r][1];for(let n=-2;n<=2;n++)for(let r=-2;r<=2;r++)-2===n||2===n||-2===r||2===r||0===n&&0===r?e.set(t+n,o+r,!0,!0):e.set(t+n,o+r,!1,!0)}}(v,n),h(v,i,0),n>=7&&function(e,t){const n=e.size,r=s.getEncodedBits(t);let o,a,l;for(let i=0;i<18;i++)o=Math.floor(i/3),a=i%3+n-8-3,l=1==(r>>i&1),e.set(o,a,l,!0),e.set(a,o,l,!0)}(v,n),function(e,t){const n=e.size;let r=-1,o=n-1,a=7,l=0;for(let i=n-1;i>0;i-=2)for(6===i&&i--;;){for(let n=0;n<2;n++)if(!e.isReserved(o,i-n)){let r=!1;l<t.length&&(r=1==(t[l]>>>a&1)),e.set(o,i-n,r),a--,-1===a&&(l++,a=7)}if(o+=r,o<0||n<=o){o-=r,r=-r;break}}}(v,p),isNaN(u)&&(u=l.getBestMask(v,h.bind(null,v,i))),l.applyMask(u,v),h(v,i,u),{modules:v,version:n,errorCorrectionLevel:i,maskPattern:u,segments:c}}return O.create=function(n,r){if(void 0===n||""===n)throw new Error("No input text");let o,a,i=t.M;return void 0!==r&&(i=t.from(r.errorCorrectionLevel,t.M),o=s.from(r.version),a=l.from(r.maskPattern),r.toSJISFunc&&e.setToSJISFunction(r.toSJISFunc)),p(n,o,i,a)},O}var Qe,We,Xe={},Ze={};function Ge(){return Qe||(Qe=1,function(e){function t(e){if("number"==typeof e&&(e=e.toString()),"string"!=typeof e)throw new Error("Color should be defined as hex string");let t=e.slice().replace("#","").split("");if(t.length<3||5===t.length||t.length>8)throw new Error("Invalid hex color: "+e);3!==t.length&&4!==t.length||(t=Array.prototype.concat.apply([],t.map(function(e){return[e,e]}))),6===t.length&&t.push("F","F");const n=parseInt(t.join(""),16);return{r:n>>24&255,g:n>>16&255,b:n>>8&255,a:255&n,hex:"#"+t.slice(0,6).join("")}}e.getOptions=function(e){e||(e={}),e.color||(e.color={});const n=void 0===e.margin||null===e.margin||e.margin<0?4:e.margin,r=e.width&&e.width>=21?e.width:void 0,o=e.scale||4;return{width:r,scale:r?4:o,margin:n,color:{dark:t(e.color.dark||"#000000ff"),light:t(e.color.light||"#ffffffff")},type:e.type,rendererOpts:e.rendererOpts||{}}},e.getScale=function(e,t){return t.width&&t.width>=e+2*t.margin?t.width/(e+2*t.margin):t.scale},e.getImageWidth=function(t,n){const r=e.getScale(t,n);return Math.floor((t+2*n.margin)*r)},e.qrToImageData=function(t,n,r){const o=n.modules.size,a=n.modules.data,l=e.getScale(o,r),i=Math.floor((o+2*r.margin)*l),u=r.margin*l,s=[r.color.light,r.color.dark];for(let e=0;e<i;e++)for(let n=0;n<i;n++){let c=4*(e*i+n),d=r.color.light;if(e>=u&&n>=u&&e<i-u&&n<i-u){d=s[a[Math.floor((e-u)/l)*o+Math.floor((n-u)/l)]?1:0]}t[c++]=d.r,t[c++]=d.g,t[c++]=d.b,t[c]=d.a}}}(Ze)),Ze}function et(){return We||(We=1,function(e){const t=Ge();e.render=function(e,n,r){let o=r,a=n;void 0!==o||n&&n.getContext||(o=n,n=void 0),n||(a=function(){try{return document.createElement("canvas")}catch(e){throw new Error("You need to specify a canvas element")}}()),o=t.getOptions(o);const l=t.getImageWidth(e.modules.size,o),i=a.getContext("2d"),u=i.createImageData(l,l);return t.qrToImageData(u.data,e,o),function(e,t,n){e.clearRect(0,0,t.width,t.height),t.style||(t.style={}),t.height=n,t.width=n,t.style.height=n+"px",t.style.width=n+"px"}(i,a,l),i.putImageData(u,0,0),a},e.renderToDataURL=function(t,n,r){let o=r;void 0!==o||n&&n.getContext||(o=n,n=void 0),o||(o={});const a=e.render(t,n,o),l=o.type||"image/png",i=o.rendererOpts||{};return a.toDataURL(l,i.quality)}}(Xe)),Xe}var tt,nt,rt={};function ot(){if(tt)return rt;tt=1;const e=Ge();function t(e,t){const n=e.a/255,r=t+'="'+e.hex+'"';return n<1?r+" "+t+'-opacity="'+n.toFixed(2).slice(1)+'"':r}function n(e,t,n){let r=e+t;return void 0!==n&&(r+=" "+n),r}return rt.render=function(r,o,a){const l=e.getOptions(o),i=r.modules.size,u=r.modules.data,s=i+2*l.margin,c=l.color.light.a?"<path "+t(l.color.light,"fill")+' d="M0 0h'+s+"v"+s+'H0z"/>':"",d="<path "+t(l.color.dark,"stroke")+' d="'+function(e,t,r){let o="",a=0,l=!1,i=0;for(let u=0;u<e.length;u++){const s=Math.floor(u%t),c=Math.floor(u/t);s||l||(l=!0),e[u]?(i++,u>0&&s>0&&e[u-1]||(o+=l?n("M",s+r,.5+c+r):n("m",a,0),a=0,l=!1),s+1<t&&e[u+1]||(o+=n("h",i),i=0)):a++}return o}(u,i,l.margin)+'"/>',f='viewBox="0 0 '+s+" "+s+'"',h='<svg xmlns="http://www.w3.org/2000/svg" '+(l.width?'width="'+l.width+'" height="'+l.width+'" ':"")+f+' shape-rendering="crispEdges">'+c+d+"</svg>\n";return"function"==typeof a&&a(null,h),h},rt}const at=E(function(){if(nt)return Y;nt=1;const e=K?J:(K=1,J=function(){return"function"==typeof Promise&&Promise.prototype&&Promise.prototype.then}),t=Oe(),n=et(),r=ot();function o(n,r,o,a,l){const i=[].slice.call(arguments,1),u=i.length,s="function"==typeof i[u-1];if(!s&&!e())throw new Error("Callback required as last argument");if(!s){if(u<1)throw new Error("Too few arguments provided");return 1===u?(o=r,r=a=void 0):2!==u||r.getContext||(a=o,o=r,r=void 0),new Promise(function(e,l){try{const l=t.create(o,a);e(n(l,r,a))}catch(i){l(i)}})}if(u<2)throw new Error("Too few arguments provided");2===u?(l=o,o=r,r=a=void 0):3===u&&(r.getContext&&void 0===l?(l=a,a=void 0):(l=a,a=o,o=r,r=void 0));try{const e=t.create(o,a);l(null,n(e,r,a))}catch(c){l(c)}}return Y.create=t.create,Y.toCanvas=o.bind(null,n.render),Y.toDataURL=o.bind(null,n.renderToDataURL),Y.toString=o.bind(null,function(e,t,n){return r.render(e,n)}),Y}()),lt={class:"login-container"},it={class:"login-card"},ut={class:"qrcode-login"},st={class:"proxy-actions"},ct={class:"qrcode-section"},dt={key:0,class:"qrcode-placeholder"},ft={key:1,class:"qrcode-display"},ht={class:"qrcode-content"},gt=["src"],pt={class:"password-login"},mt={class:"proxy-actions"},vt={class:"login-footer"},yt=r(i({__name:"login",setup(r){const i=x(),E=t(),J=u("qrcode"),K=u(!1),Y=u(""),$=u(""),O=u(""),Q=s({deviceType:"iPad",deviceId:"",deviceName:"微信机器人",proxy:{ProxyIp:"",ProxyUser:"",ProxyPassword:"",Host:"",Port:0,Type:"socks5"}}),W=s({username:"",password:"",data62:"",deviceName:"微信机器人",proxy:{ProxyIp:"",ProxyUser:"",ProxyPassword:"",Host:"",Port:0,Type:"socks5"}}),X=[{label:"iPad",value:"iPad"},{label:"iPad X",value:"iPadX"},{label:"Windows",value:"Windows"},{label:"Windows UWP",value:"WindowsUwp"},{label:"Windows Unified",value:"WindowsUnified"},{label:"Car",value:"Car"},{label:"Android Pad",value:"AndroidPad"}];c(()=>{Q.deviceId=Z()});const Z=()=>"WX"+Date.now().toString(36)+Math.random().toString(36).substr(2,9),G=()=>e(null,null,function*(){if(Q.deviceId&&Q.deviceName){K.value=!0;try{const e=yield n.getQRCode("Car",{DeviceId:Q.deviceId,DeviceName:Q.deviceName,DeviceType:"car-31"});if(!e.Success||!e.Data)throw new Error(e.Message||"生成二维码失败");$.value=e.Data.qrCodeData||e.Data.QrCodeData,Y.value=yield at.toDataURL($.value),O.value=e.Data.uuid||e.Data.Uuid,j.success("二维码生成成功，请使用微信扫码登录"),ne()}catch(e){j.error("生成二维码失败，请检查网络连接")}finally{K.value=!1}}else j.error("请填写设备ID和设备名称")}),ee=()=>e(null,null,function*(){var e;if(W.username&&W.password&&W.data62){K.value=!0;try{const t=yield n.loginWithData62({Username:W.username,Password:W.password,Data62:W.data62,DeviceName:Q.deviceName||"Device_Login",Proxy:W.proxy});if(!t.Success||!(null==(e=t.Data)?void 0:e.wxid))throw new Error(t.Message||"登录失败");{const e={wxid:t.Data.wxid,nickname:t.Data.nickname||W.username,avatar:t.Data.avatar||"",status:"online",deviceType:"Data62",deviceId:Z(),deviceName:Q.deviceName||"Device_Login",loginTime:new Date,proxy:W.proxy.ProxyIp?W.proxy:void 0};E.addAccount(e),j.success("登录成功"),i.push("/dashboard")}}catch(t){j.error("登录失败，请检查账号密码")}finally{K.value=!1}}else j.error("请填写完整的登录信息")});let te=null;const ne=()=>{te&&clearInterval(te),O.value?te=setInterval(()=>e(null,null,function*(){try{const e=yield n.checkQRCodeStatus({Uuid:O.value});if(e.Success&&"登录成功"===e.Message){if(clearInterval(te),te=null,j.success("扫码登录成功！正在初始化..."),e.Data&&e.Data.wxid){yield ae(e.Data.wxid);const t={wxid:e.Data.wxid,nickname:e.Data.nickname||Q.deviceName,avatar:e.Data.avatar||"",status:"online",deviceType:"Car",deviceId:Q.deviceId,deviceName:Q.deviceName,loginTime:new Date,proxy:W.proxy.ProxyIp?W.proxy:void 0};E.addAccount(t)}setTimeout(()=>{i.push("/dashboard")},2e3)}else if(e.Success&&e.Data){const t=e.Data;t.expiredTime<=0?(clearInterval(te),te=null,j.error("二维码已过期，请重新生成"),le()):0===t.status||(1===t.status?re(t):4===t.status&&(clearInterval(te),te=null,oe(t)))}}catch(e){}}),2e3):j.error("二维码UUID无效")},re=e=>{const t=document.querySelector(".qrcode-display");if(!t)return;const n=t.querySelector(".user-scanned-overlay");n&&n.remove();const r=document.createElement("div");r.className="user-scanned-overlay",r.innerHTML=`\n    <div class="user-info">\n      <img src="${e.headImgUrl}"\n           alt="用户头像"\n           class="user-avatar"\n           onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';" />\n      <div class="avatar-fallback" style="display: none; width: 80px; height: 80px; border-radius: 50%; background: var(--el-color-primary); color: white; align-items: center; justify-content: center; font-size: 24px; font-weight: 600;">\n        ${e.nickName?e.nickName.charAt(0).toUpperCase():"?"}\n      </div>\n      <div class="user-details">\n        <div class="user-nickname">${e.nickName}</div>\n        <div class="scan-status">已扫码，请确认登录</div>\n      </div>\n    </div>\n    <div class="confirm-hint">\n      <el-icon><Iphone /></el-icon>\n      请在手机上确认登录\n    </div>\n  `,t.appendChild(r)},oe=e=>{const t=document.querySelector(".qrcode-display");if(!t)return;const n=t.querySelector(".user-scanned-overlay");n&&n.remove();const r=document.createElement("div");r.className="qrcode-cancelled-overlay",r.innerHTML=`\n    <div class="cancel-info">\n      <el-icon><CircleClose /></el-icon>\n      <div class="cancel-message">用户取消登录</div>\n      ${e.nickName?`<div class="cancel-user">${e.nickName} 取消了登录</div>`:""}\n    </div>\n    <button class="el-button el-button--primary" onclick="location.reload()">\n      <el-icon><Refresh /></el-icon>\n      重新获取二维码\n    </button>\n  `,t.appendChild(r)},ae=t=>e(null,null,function*(){try{const e=yield n.performSecondAuth(t);e.Success?j.success("账号初始化成功！"):j.warning(`账号初始化失败: ${e.Message}`)}catch(e){j.warning("账号初始化失败，请手动重连")}}),le=()=>{Y.value="",$.value="",O.value="",te&&(clearInterval(te),te=null)},ie=()=>{i.push("/")},ue=u([]),se=u([]),ce=u([]),de=u(""),fe=u(null),he=u(null),ge=u(!1),pe=u("none"),me=u("none"),ve=()=>e(null,null,function*(){try{const e=yield o.getAvailableProxies();if(0===e.code){ue.value=e.data.list,se.value=e.data.list;const t=new Set;e.data.list.forEach(e=>{e.country&&t.add(e.country)}),ce.value=Array.from(t).sort()}}catch(e){}}),ye=()=>{de.value?se.value=ue.value.filter(e=>e.country===de.value):se.value=ue.value,fe.value=null,he.value=null},we=()=>{fe.value&&(he.value=ue.value.find(e=>e.id===fe.value)||null,he.value&&Pe(he.value))},Pe=e=>{const t={ProxyIp:e.ip,ProxyUser:e.username,ProxyPassword:e.password,Host:e.ip,Port:e.port,Type:"socks5"};"qrcode"===J.value?Object.assign(Q.proxy,t):"password"===J.value&&Object.assign(W.proxy,t)},be=()=>{ve()},xe=()=>{"none"===pe.value&&Object.assign(Q.proxy,{ProxyIp:"",ProxyUser:"",ProxyPassword:"",Host:"",Port:0,Type:"socks5"}),fe.value=null,he.value=null},Ee=()=>{"none"===me.value&&Object.assign(W.proxy,{ProxyIp:"",ProxyUser:"",ProxyPassword:"",Host:"",Port:0,Type:"socks5"}),fe.value=null,he.value=null};return c(()=>{ve()}),(e,t)=>{const n=D,r=U,o=T,i=_,u=M,s=R,c=k,x=L,E=S,j=q,$=B,O=V,te=N,ne=A,re=C,oe=I;return p(),d("div",lt,[f("div",it,[t[43]||(t[43]=f("div",{class:"login-header"},[f("h2",null,"微信机器人登录"),f("p",null,"选择登录方式开始使用")],-1)),h(re,{modelValue:J.value,"onUpdate:modelValue":t[24]||(t[24]=e=>J.value=e),class:"login-tabs"},{default:g(()=>[h(ne,{label:"二维码登录",name:"qrcode"},{default:g(()=>[f("div",ut,[h(te,{model:Q,"label-width":"100px"},{default:g(()=>[h(o,{label:"设备类型"},{default:g(()=>[h(r,{modelValue:Q.deviceType,"onUpdate:modelValue":t[0]||(t[0]=e=>Q.deviceType=e),placeholder:"选择设备类型"},{default:g(()=>[(p(),d(m,null,v(X,e=>h(n,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),h(o,{label:"设备ID"},{default:g(()=>[h(u,{modelValue:Q.deviceId,"onUpdate:modelValue":t[2]||(t[2]=e=>Q.deviceId=e),placeholder:"自动生成",readonly:""},{append:g(()=>[h(i,{onClick:t[1]||(t[1]=e=>Q.deviceId=Z())},{default:g(()=>t[26]||(t[26]=[y(" 重新生成 ",-1)])),_:1,__:[26]})]),_:1},8,["modelValue"])]),_:1}),h(o,{label:"设备名称"},{default:g(()=>[h(u,{modelValue:Q.deviceName,"onUpdate:modelValue":t[3]||(t[3]=e=>Q.deviceName=e),placeholder:"输入设备名称"},null,8,["modelValue"])]),_:1}),h(O,null,{default:g(()=>[h($,{title:"代理设置（可选）",name:"proxy"},{default:g(()=>[h(o,{label:"代理配置"},{default:g(()=>[h(c,{modelValue:pe.value,"onUpdate:modelValue":t[4]||(t[4]=e=>pe.value=e),onChange:xe},{default:g(()=>[h(s,{value:"none"},{default:g(()=>t[27]||(t[27]=[y("不使用代理",-1)])),_:1,__:[27]}),h(s,{value:"preset"},{default:g(()=>t[28]||(t[28]=[y("选择已有代理",-1)])),_:1,__:[28]}),h(s,{value:"manual"},{default:g(()=>t[29]||(t[29]=[y("手动配置",-1)])),_:1,__:[29]})]),_:1},8,["modelValue"])]),_:1}),"preset"===pe.value?(p(),d(m,{key:0},[h(o,{label:"地区筛选"},{default:g(()=>[h(r,{modelValue:de.value,"onUpdate:modelValue":t[5]||(t[5]=e=>de.value=e),placeholder:"选择地区",clearable:"",onChange:ye},{default:g(()=>[h(n,{label:"全部地区",value:""}),(p(!0),d(m,null,v(ce.value,e=>(p(),P(n,{key:e,label:e,value:e},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),h(o,{label:"选择代理"},{default:g(()=>[h(r,{modelValue:fe.value,"onUpdate:modelValue":t[6]||(t[6]=e=>fe.value=e),placeholder:"选择一个可用的代理",filterable:"",onChange:we,style:{width:"100%"}},{default:g(()=>[(p(!0),d(m,null,v(se.value,e=>(p(),P(n,{key:e.id,label:b(l)(e),value:e.id,disabled:"active"!==e.status},null,8,["label","value","disabled"]))),128))]),_:1},8,["modelValue"])]),_:1}),he.value?(p(),P(o,{key:0},{default:g(()=>[h(x,{title:`已选择代理: ${he.value.ip}:${he.value.port} [${he.value.country||"未知地区"}]`,type:"success",closable:!1,"show-icon":""},null,8,["title"])]),_:1})):w("",!0),f("div",st,[h(i,{size:"small",onClick:be},{default:g(()=>[h(E,null,{default:g(()=>[h(b(z))]),_:1}),t[30]||(t[30]=y(" 刷新列表 ",-1))]),_:1,__:[30]}),h(i,{size:"small",type:"primary",onClick:t[7]||(t[7]=e=>ge.value=!0)},{default:g(()=>[h(E,null,{default:g(()=>[h(b(H))]),_:1}),t[31]||(t[31]=y(" 管理代理 ",-1))]),_:1,__:[31]})])],64)):w("",!0),"manual"===pe.value?(p(),d(m,{key:1},[h(o,{label:"代理IP"},{default:g(()=>[h(u,{modelValue:Q.proxy.ProxyIp,"onUpdate:modelValue":t[8]||(t[8]=e=>Q.proxy.ProxyIp=e),placeholder:"代理服务器IP"},null,8,["modelValue"])]),_:1}),h(o,{label:"代理端口"},{default:g(()=>[h(j,{modelValue:Q.proxy.Port,"onUpdate:modelValue":t[9]||(t[9]=e=>Q.proxy.Port=e),placeholder:"代理端口"},null,8,["modelValue"])]),_:1}),h(o,{label:"用户名"},{default:g(()=>[h(u,{modelValue:Q.proxy.ProxyUser,"onUpdate:modelValue":t[10]||(t[10]=e=>Q.proxy.ProxyUser=e),placeholder:"代理用户名（可选）"},null,8,["modelValue"])]),_:1}),h(o,{label:"密码"},{default:g(()=>[h(u,{modelValue:Q.proxy.ProxyPassword,"onUpdate:modelValue":t[11]||(t[11]=e=>Q.proxy.ProxyPassword=e),type:"password",placeholder:"代理密码（可选）"},null,8,["modelValue"])]),_:1})],64)):w("",!0)]),_:1})]),_:1})]),_:1},8,["model"]),f("div",ct,[Y.value?(p(),d("div",ft,[f("div",ht,[f("img",{src:Y.value,alt:"登录二维码"},null,8,gt),t[34]||(t[34]=f("p",null,"请使用微信扫描二维码登录",-1)),h(i,{onClick:le,link:""},{default:g(()=>t[33]||(t[33]=[y("重新生成",-1)])),_:1,__:[33]})])])):(p(),d("div",dt,[h(E,{size:"64",color:"#ccc"},{default:g(()=>[h(b(F))]),_:1}),t[32]||(t[32]=f("p",null,"点击生成二维码",-1))]))]),h(i,{type:"primary",size:"large",onClick:G,loading:K.value,class:"login-button"},{default:g(()=>t[35]||(t[35]=[y(" 生成二维码 ",-1)])),_:1,__:[35]},8,["loading"])])]),_:1}),h(ne,{label:"账号密码登录",name:"password"},{default:g(()=>[f("div",pt,[h(te,{model:W,"label-width":"100px"},{default:g(()=>[h(o,{label:"用户名"},{default:g(()=>[h(u,{modelValue:W.username,"onUpdate:modelValue":t[12]||(t[12]=e=>W.username=e),placeholder:"输入微信号或手机号"},null,8,["modelValue"])]),_:1}),h(o,{label:"密码"},{default:g(()=>[h(u,{modelValue:W.password,"onUpdate:modelValue":t[13]||(t[13]=e=>W.password=e),type:"password",placeholder:"输入密码"},null,8,["modelValue"])]),_:1}),h(o,{label:"Data62"},{default:g(()=>[h(u,{modelValue:W.data62,"onUpdate:modelValue":t[14]||(t[14]=e=>W.data62=e),type:"textarea",rows:3,placeholder:"输入Data62数据"},null,8,["modelValue"])]),_:1}),h(o,{label:"设备名称"},{default:g(()=>[h(u,{modelValue:W.deviceName,"onUpdate:modelValue":t[15]||(t[15]=e=>W.deviceName=e),placeholder:"输入设备名称"},null,8,["modelValue"])]),_:1}),h(O,null,{default:g(()=>[h($,{title:"代理设置（可选）",name:"proxy"},{default:g(()=>[h(o,{label:"代理配置"},{default:g(()=>[h(c,{modelValue:me.value,"onUpdate:modelValue":t[16]||(t[16]=e=>me.value=e),onChange:Ee},{default:g(()=>[h(s,{value:"none"},{default:g(()=>t[36]||(t[36]=[y("不使用代理",-1)])),_:1,__:[36]}),h(s,{value:"preset"},{default:g(()=>t[37]||(t[37]=[y("选择已有代理",-1)])),_:1,__:[37]}),h(s,{value:"manual"},{default:g(()=>t[38]||(t[38]=[y("手动配置",-1)])),_:1,__:[38]})]),_:1},8,["modelValue"])]),_:1}),"preset"===me.value?(p(),d(m,{key:0},[h(o,{label:"地区筛选"},{default:g(()=>[h(r,{modelValue:de.value,"onUpdate:modelValue":t[17]||(t[17]=e=>de.value=e),placeholder:"选择地区",clearable:"",onChange:ye},{default:g(()=>[h(n,{label:"全部地区",value:""}),(p(!0),d(m,null,v(ce.value,e=>(p(),P(n,{key:e,label:e,value:e},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),h(o,{label:"选择代理"},{default:g(()=>[h(r,{modelValue:fe.value,"onUpdate:modelValue":t[18]||(t[18]=e=>fe.value=e),placeholder:"选择一个可用的代理",filterable:"",onChange:we,style:{width:"100%"}},{default:g(()=>[(p(!0),d(m,null,v(se.value,e=>(p(),P(n,{key:e.id,label:b(l)(e),value:e.id,disabled:"active"!==e.status},null,8,["label","value","disabled"]))),128))]),_:1},8,["modelValue"])]),_:1}),he.value?(p(),P(o,{key:0},{default:g(()=>[h(x,{title:`已选择代理: ${he.value.ip}:${he.value.port} [${he.value.country||"未知地区"}]`,type:"success",closable:!1,"show-icon":""},null,8,["title"])]),_:1})):w("",!0),f("div",mt,[h(i,{size:"small",onClick:be},{default:g(()=>[h(E,null,{default:g(()=>[h(b(z))]),_:1}),t[39]||(t[39]=y(" 刷新列表 ",-1))]),_:1,__:[39]}),h(i,{size:"small",type:"primary",onClick:t[19]||(t[19]=e=>ge.value=!0)},{default:g(()=>[h(E,null,{default:g(()=>[h(b(H))]),_:1}),t[40]||(t[40]=y(" 管理代理 ",-1))]),_:1,__:[40]})])],64)):w("",!0),"manual"===me.value?(p(),d(m,{key:1},[h(o,{label:"代理IP"},{default:g(()=>[h(u,{modelValue:W.proxy.ProxyIp,"onUpdate:modelValue":t[20]||(t[20]=e=>W.proxy.ProxyIp=e),placeholder:"代理服务器IP"},null,8,["modelValue"])]),_:1}),h(o,{label:"代理端口"},{default:g(()=>[h(j,{modelValue:W.proxy.Port,"onUpdate:modelValue":t[21]||(t[21]=e=>W.proxy.Port=e),placeholder:"代理端口"},null,8,["modelValue"])]),_:1}),h(o,{label:"用户名"},{default:g(()=>[h(u,{modelValue:W.proxy.ProxyUser,"onUpdate:modelValue":t[22]||(t[22]=e=>W.proxy.ProxyUser=e),placeholder:"代理用户名（可选）"},null,8,["modelValue"])]),_:1}),h(o,{label:"密码"},{default:g(()=>[h(u,{modelValue:W.proxy.ProxyPassword,"onUpdate:modelValue":t[23]||(t[23]=e=>W.proxy.ProxyPassword=e),type:"password",placeholder:"代理密码（可选）"},null,8,["modelValue"])]),_:1})],64)):w("",!0)]),_:1})]),_:1})]),_:1},8,["model"]),h(i,{type:"primary",size:"large",onClick:ee,loading:K.value,class:"login-button"},{default:g(()=>t[41]||(t[41]=[y(" 登录 ",-1)])),_:1,__:[41]},8,["loading"])])]),_:1})]),_:1},8,["modelValue"]),f("div",vt,[h(i,{onClick:ie},{default:g(()=>t[42]||(t[42]=[y("返回首页",-1)])),_:1,__:[42]})])]),h(oe,{modelValue:ge.value,"onUpdate:modelValue":t[25]||(t[25]=e=>ge.value=e),title:"代理管理",width:"80%",top:"5vh"},{default:g(()=>[h(a,{onProxyUpdated:be})]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-2ace58db"]]);export{yt as default};

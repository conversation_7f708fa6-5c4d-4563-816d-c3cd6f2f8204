var e=Object.defineProperty,t=Object.defineProperties,a=Object.getOwnPropertyDescriptors,n=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable,o=(t,a,n)=>a in t?e(t,a,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[a]=n,r=(e,t)=>{for(var a in t||(t={}))s.call(t,a)&&o(e,a,t[a]);if(n)for(var a of n(t))i.call(t,a)&&o(e,a,t[a]);return e},l=(e,n)=>t(e,a(n)),c=(e,t,a)=>o(e,"symbol"!=typeof t?t+"":t,a),d=(e,t,a)=>new Promise((n,s)=>{var i=e=>{try{r(a.next(e))}catch(t){s(t)}},o=e=>{try{r(a.throw(e))}catch(t){s(t)}},r=e=>e.done?n(e.value):Promise.resolve(e.value).then(i,o);r((a=a.apply(e,t)).next())});import{E as u,a as m,l as h,p as g,b as f,v as p,d as v,f as y,c as w,e as C,g as x,h as T,i as b}from"./element-plus-DAEtRXw7.js";import{av as D,r as S,c as I,t as A,f as M,v as _,x as L,M as U,A as k,E as W,u as N,H as E,K as F,D as R,J as O,ad as P,y as j,G as $}from"./vue-C-lRF7aZ.js";import{a as K}from"./utils-XWYVm-q4.js";const B=new class{constructor(){c(this,"cache",new Map),c(this,"CACHE_EXPIRE_TIME",6048e5),c(this,"MAX_CACHE_SIZE",500),c(this,"STORAGE_KEY","wechat_file_cache"),c(this,"currentWxid","")}setCurrentWxid(e){this.currentWxid!==e?(this.currentWxid=e,0===this.cache.size&&this.loadCacheFromStorage()):this.currentWxid===e&&0===this.cache.size&&this.loadCacheFromStorage()}generateCacheKey(e,t){return`${e}_${t}`}generateStorageKey(){return this.STORAGE_KEY}calculateFileHash(e){return d(this,null,function*(){const t=yield e.arrayBuffer(),a=yield crypto.subtle.digest("SHA-256",t);return Array.from(new Uint8Array(a)).map(e=>e.toString(16).padStart(2,"0")).join("")})}loadCacheFromStorage(){if(this.currentWxid)try{const e=this.generateStorageKey(),t=localStorage.getItem(e);if(t){const e=JSON.parse(t);this.cache=new Map(e),this.cleanExpiredCache()}else this.cache.clear()}catch(e){this.cache.clear()}}saveCacheToStorage(){if(this.currentWxid)try{const e=this.generateStorageKey(),t=Array.from(this.cache.entries());localStorage.setItem(e,JSON.stringify(t))}catch(e){}}addFileToCache(e){const t=this.generateCacheKey(e.fileName,e.fileSize),a=l(r({},e),{cacheTime:Date.now(),lastUsed:Date.now()});this.cache.set(t,a),this.cleanExpiredCache(),this.saveCacheToStorage()}findCachedFile(e){return d(this,null,function*(){const t=this.generateCacheKey(e.name,e.size),a=this.cache.get(t);if(!a)return null;if(Date.now()-a.cacheTime>this.CACHE_EXPIRE_TIME)return this.cache.delete(t),null;if(a.fileHash)try{if((yield this.calculateFileHash(e))!==a.fileHash)return null}catch(n){}return a.lastUsed=Date.now(),this.cache.set(t,a),this.saveCacheToStorage(),a})}cleanExpiredCache(){const e=Date.now(),t=[];let a=0;for(const[n,s]of this.cache.entries())e-s.cacheTime>this.CACHE_EXPIRE_TIME&&t.push(n);if(t.forEach(e=>{this.cache.delete(e)}),this.cache.size>this.MAX_CACHE_SIZE){const e=Array.from(this.cache.entries()).sort(([,e],[,t])=>e.lastUsed-t.lastUsed).slice(0,this.cache.size-this.MAX_CACHE_SIZE);e.forEach(([e])=>{this.cache.delete(e)}),a=e.length}(t.length>0||a>0)&&this.saveCacheToStorage()}getCacheStats(){return{size:this.cache.size,files:Array.from(this.cache.values()).map(e=>({fileName:e.fileName,fileSize:e.fileSize,cacheTime:e.cacheTime}))}}clearCache(){if(this.cache.clear(),this.currentWxid)try{const e=this.generateStorageKey();localStorage.removeItem(e)}catch(e){}}removeCachedFile(e,t){const a=this.generateCacheKey(e,t),n=this.cache.delete(a);return n&&this.saveCacheToStorage(),n}};function z(){Object.keys(localStorage).filter(e=>e.startsWith("wechat_file_cache_")&&"wechat_file_cache"!==e).forEach(e=>{try{localStorage.removeItem(e)}catch(t){}})}function G(e){if(0===e)return"0 B";const t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["B","KB","MB","GB","TB"][t]}"undefined"!=typeof window&&(window.debugFileCache=function(){Object.keys(localStorage).filter(e=>e.startsWith("wechat_file_cache_")).forEach(e=>{try{const t=localStorage.getItem(e);t&&JSON.parse(t)}catch(t){}})});const H=new class{constructor(){c(this,"instance"),c(this,"baseURL"),c(this,"timeout"),this.baseURL="http://localhost:8059/api",this.timeout=1e4,this.instance=K.create({baseURL:this.baseURL,timeout:this.timeout,headers:{"Content-Type":"application/json"}}),this.setupInterceptors()}setupInterceptors(){this.instance.interceptors.request.use(e=>{"get"===e.method&&(e.params=l(r({},e.params),{_t:Date.now()}));const t=localStorage.getItem("auth_token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),this.instance.interceptors.response.use(e=>{const{data:t}=e;if("blob"===e.config.responseType)return e;if(!1===t.success){const e=t.message||"请求失败";return 401===t.code?(localStorage.removeItem("auth_token"),window.location.href="/login",Promise.reject(new Error("未授权访问"))):Promise.reject(new Error(e))}return t},e=>{let t="网络请求失败";if(e.response){const{status:a,data:n}=e.response;switch(a){case 400:t=(null==n?void 0:n.message)||"请求参数错误";break;case 401:t="未授权访问",localStorage.removeItem("auth_token"),window.location.href="/login";break;case 403:t="禁止访问";break;case 404:t="请求的资源不存在";break;case 500:t="服务器内部错误";break;case 502:t="网关错误";break;case 503:t="服务不可用";break;default:t=(null==n?void 0:n.message)||`请求失败 (${a})`}}else e.request&&(t="ECONNABORTED"===e.code?"请求超时，请稍后重试":"网络连接异常，请检查网络设置");return Promise.reject(new Error(t))})}get(e,t,a){return d(this,null,function*(){try{return yield this.instance.get(e,r({params:t},a))}catch(n){throw!1!==(null==a?void 0:a.showError)&&u.error(n.message),n}})}post(e,t,a){return d(this,null,function*(){try{return yield this.instance.post(e,t,a)}catch(n){throw!1!==(null==a?void 0:a.showError)&&u.error(n.message),n}})}put(e,t,a){return d(this,null,function*(){try{return yield this.instance.put(e,t,a)}catch(n){throw!1!==(null==a?void 0:a.showError)&&u.error(n.message),n}})}delete(e,t){return d(this,null,function*(){try{return yield this.instance.delete(e,t)}catch(a){throw!1!==(null==t?void 0:t.showError)&&u.error(a.message),a}})}upload(e,t,a){return d(this,null,function*(){const n=new FormData;n.append("file",t);try{return yield this.instance.post(e,n,r({headers:{"Content-Type":"multipart/form-data"}},a))}catch(s){throw!1!==(null==a?void 0:a.showError)&&u.error(s.message),s}})}download(e,t,a){return d(this,null,function*(){try{const n=yield this.instance.get(e,r({responseType:"blob"},a)),s=new Blob([n.data]),i=window.URL.createObjectURL(s),o=document.createElement("a");o.href=i,o.download=t||"download",document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(i)}catch(n){throw!1!==(null==a?void 0:a.showError)&&u.error(n.message),n}})}setBaseURL(e){this.baseURL=e,this.instance.defaults.baseURL=e}setTimeout(e){this.timeout=e,this.instance.defaults.timeout=e}getInstance(){return this.instance}},V={getQRCode:(e,t)=>d(null,null,function*(){const a={iPad:"/Login/LoginGetQR",iPadX:"/Login/LoginGetQRx",Windows:"/Login/LoginGetQRWin",WindowsUwp:"/Login/LoginGetQRWinUwp",WindowsUnified:"/Login/LoginGetQRWinUnified",Mac:"/Login/LoginGetQRMac",AndroidPad:"/Login/LoginGetQRPad",AndroidPadX:"/Login/LoginGetQRPadx",Car:"/Login/LoginGetQRCar"},n=a[e]||a.iPad;return yield H.post(n,t)}),loginWithData62:e=>d(null,null,function*(){return yield H.post("/Login/Data62Login",e)}),loginWithA16:e=>d(null,null,function*(){return yield H.post("/Login/A16Data",e)}),checkQRCodeStatus:e=>d(null,null,function*(){return yield H.post(`/Login/LoginCheckQR?uuid=${encodeURIComponent(e.Uuid)}`)}),performSecondAuth:e=>d(null,null,function*(){return yield H.post(`/Login/LoginTwiceAutoAuth?wxid=${encodeURIComponent(e)}`)}),autoHeartBeat:e=>d(null,null,function*(){return yield H.post(`/Login/AutoHeartBeat?wxid=${encodeURIComponent(e)}`)}),logout:e=>d(null,null,function*(){return yield H.post("/Login/Logout",{Wxid:e})}),getLoggedAccounts:()=>d(null,null,function*(){return yield H.get("/Login/GetLoggedAccounts",{api_key:"api_kedaya"})}),setProxy:e=>d(null,null,function*(){return yield H.post("/Tools/setproxy",e)}),getProxy:e=>d(null,null,function*(){return yield H.post("/Tools/getproxy",e)}),testProxy:e=>d(null,null,function*(){return yield H.post("/Tools/testproxy",e)}),getQRCodeForDeviceReuse:e=>d(null,null,function*(){return yield H.post("/Login/LoginGetQRCar",e)})},Q=D("auth",()=>{const e=S([]),t=S(null),a=S(!1),n=I(()=>e.value.length>0),s=I(()=>e.value.filter(e=>"online"===e.status));return{accounts:e,currentAccount:t,isLoading:a,isLoggedIn:n,onlineAccounts:s,addAccount:a=>{const n=e.value.findIndex(e=>e.wxid===a.wxid);n>=0?e.value[n]=a:e.value.push(a),t.value||(t.value=a)},removeAccount:a=>{var n;const s=e.value.findIndex(e=>e.wxid===a);s>=0&&(e.value.splice(s,1),(null==(n=t.value)?void 0:n.wxid)===a&&(t.value=e.value[0]||null))},updateAccountStatus:(t,a)=>{const n=e.value.find(e=>e.wxid===t);n&&(n.status=a)},setCurrentAccount:a=>{const n=e.value.find(e=>e.wxid===a);if(n){const e=t.value;t.value=n,B.setCurrentWxid(a),e&&e.wxid}},loginWithQRCode:(e,t,n,s)=>d(null,null,function*(){a.value=!0;try{return yield V.getQRCode(e,{DeviceID:t,DeviceName:n,Proxy:s||{}})}finally{a.value=!1}}),loginWithPassword:(e,t,n,s,i)=>d(null,null,function*(){a.value=!0;try{return yield V.loginWithData62({UserName:e,Password:t,Data62:n,DeviceName:s,Proxy:i||{}})}finally{a.value=!1}}),checkLoginStatus:e=>d(null,null,function*(){try{return!0}catch(e){return!1}}),fetchLoggedAccounts:()=>d(null,null,function*(){a.value=!0;try{const a=yield V.getLoggedAccounts();let n=[];if(n=a&&Array.isArray(a.Data)?a.Data:a&&Array.isArray(a.data)?a.data:Array.isArray(a)?a:[],e.value=n.map(e=>({wxid:e.wxid||"",nickname:e.nickname||"",avatar:e.headUrl||e.avatar||"",headUrl:e.headUrl||e.avatar||"",status:"online"===e.status?"online":"offline",deviceType:e.deviceType||"Unknown",deviceId:e.deviceId||e.imei||"",deviceName:e.deviceName||"",loginTime:e.loginTime,refreshTokenDate:e.refreshTokenDate,uin:e.uin||0,alias:e.alias||"",email:e.email||"",mobile:e.mobile||"",imei:e.imei||"",loginMode:e.loginMode||"",osVersion:e.osVersion||"",romModel:e.romModel||"",softType:e.softType||""})),e.value.length>0&&!t.value){const a=e.value.find(e=>"online"===e.status)||e.value[0];t.value=a,a.wxid&&B.setCurrentWxid(a.wxid)}return e.value}catch(n){return[]}finally{a.value=!1}})}}),J={searchContact:e=>q.post("/Friend/Search",e),sendFriendRequest:e=>q.post("/Friend/SendRequest",e),getFriendList:e=>q.post("/Friend/GetTotalContractList",e),acceptFriendRequest:e=>q.post("/Friend/PassVerify",e),getAutoAcceptFriendStatus:e=>q.get(`/Friend/AutoAcceptFriendGetStatus?wxid=${e.Wxid}`),setAutoAcceptFriendStatus:e=>q.get(`/Friend/AutoAcceptFriendSetStatus?wxid=${e.Wxid}&enable=${e.Enable}`),deleteFriend:e=>q.post("/Friend/Delete",e),uploadContacts:e=>q.post("/Friend/Upload",e),getMobileContacts:e=>q.post(`/Friend/GetMFriend?wxid=${e}`),setFriendRemark:e=>q.post("/Friend/SetRemarks",e),blockFriend:e=>q.post("/Friend/Blacklist",e),getFriendDetail:e=>q.post("/Friend/GetContractDetail",e),getContactList:e=>q.post("/Friend/GetContractList",e),getTotalContactList:e=>q.post("/Friend/GetTotalContractList",e),getSimplifiedContactList:e=>q.post("/Friend/GetSimplifiedContractList",e),getFriendState:e=>q.post("/Friend/GetFriendstate",e),findNearbyPeople:e=>q.post("/Friend/LbsFind",e)},q=K.create({baseURL:"http://localhost:8059/api",timeout:3e4,headers:{"Content-Type":"application/json"}});q.interceptors.request.use(e=>e,e=>Promise.reject(e)),q.interceptors.response.use(e=>e.data,e=>{if(e.response){const{status:t,data:a}=e.response;return Promise.reject(a||e.response)}return e.request?Promise.reject(new Error("网络连接错误")):Promise.reject(e)});const X=e=>d(null,null,function*(){try{return yield q.post("/Tools/DownloadFile",e)}catch(t){throw t}}),Z=e=>q.post("/Msg/SendTxt",e),Y=e=>q.post("/Msg/UploadImg",e),ee=e=>q.get(`/Msg/SyncAndPush?wxid=${e}`),te=e=>q.post("/Msg/Revoke",e),ae=e=>q.post("/Msg/SendCDNFile",e),ne=e=>q.post("/Tools/CdnDownloadImage",e),se={HOST:"127.0.0.1",PORT:8088,CONNECT_TIMEOUT:1e4,RECONNECT:{MAX_ATTEMPTS:5,INTERVAL:3e3},HEARTBEAT:{INTERVAL:3e4},getUrl:e=>{const t=`ws://${se.HOST}:${se.PORT}/ws`;return e?`${t}?wxid=${e}`:t}};const ie=class e{constructor(){c(this,"connections",new Map),c(this,"maxReconnectAttempts",se.RECONNECT.MAX_ATTEMPTS),c(this,"reconnectInterval",se.RECONNECT.INTERVAL),c(this,"eventListeners",new Map),c(this,"currentWxid")}static getInstance(){return e.instance||(e.instance=new e),e.instance}on(e,t){this.eventListeners.has(e)||this.eventListeners.set(e,[]),this.eventListeners.get(e).push(t)}off(e,t){const a=this.eventListeners.get(e);if(a){const e=a.indexOf(t);e>-1&&a.splice(e,1)}}emit(e,...t){const a=this.eventListeners.get(e);a&&a.forEach(e=>e(...t))}connect(e){return new Promise((t,a)=>{if(!e)return void a(new Error("wxid不能为空"));const n=this.connections.get(e);if(n&&n.ws.readyState===WebSocket.OPEN)return this.currentWxid=e,B.setCurrentWxid(e),void t(!0);if(n&&n.ws.readyState!==WebSocket.OPEN&&this.connections.delete(e),n&&n.isConnecting)return void a(new Error("WebSocket正在连接中"));this.currentWxid=e,B.setCurrentWxid(e);const s={ws:null,reconnectAttempts:0,heartbeatInterval:null,isConnecting:!0};try{const n=se.getUrl(e),i=new WebSocket(n);s.ws=i,i.onopen=()=>{s.isConnecting=!1,s.reconnectAttempts=0,this.currentWxid=e,B.setCurrentWxid(e),this.startHeartbeat(e),this.emit("connection_status",!0),t(!0)},i.onmessage=t=>{this.handleMessage(t.data,e)},i.onclose=t=>{s.isConnecting=!1,this.stopHeartbeat(e),this.emit("connection_status",!1),!t.wasClean&&s.reconnectAttempts<this.maxReconnectAttempts&&this.scheduleReconnect(e)},i.onerror=t=>{s.isConnecting=!1,this.connections.delete(e),a(t)},this.connections.set(e,s)}catch(i){a(i)}})}disconnect(e){const t=e||this.currentWxid;if(!t)return;const a=this.connections.get(t);a&&(this.stopHeartbeat(t),a.ws&&a.ws.close(1e3,"主动断开连接"),this.connections.delete(t)),t===this.currentWxid&&(this.currentWxid=void 0)}disconnectAll(){for(const[e,t]of this.connections)this.stopHeartbeat(e),t.ws&&t.ws.close(1e3,"主动断开所有连接");this.connections.clear(),this.currentWxid=void 0}switchCurrentAccount(e){return!!this.connections.has(e)&&(this.currentWxid=e,B.setCurrentWxid(e),!0)}getConnectedAccounts(){return Array.from(this.connections.keys()).filter(e=>this.isAccountConnected(e))}hasEventListeners(){return this.eventListeners.size>0}handleMessage(e,t){try{const t=JSON.parse(e);switch(t.type){case"chat_message":this.emit("chat_message",t.data);break;case"wechat_message":this.handleWeChatMessage(t);break;case"system_message":this.emit("system_message",t.data)}}catch(a){}}handleWeChatMessage(e){e.messages&&0!==e.messages.length&&e.messages.forEach(t=>{var a,n,s,i,o,r,l,c,d;if(this.shouldFilterMessage(t))return;const u=t.isGroupMessage||(null==(a=t.fromUser)?void 0:a.includes("@chatroom"))||(null==(n=t.toUser)?void 0:n.includes("@chatroom"));let m,h=!1;h=u?t.actualSender===e.wxid:t.fromUser===e.wxid||t.actualSender===e.wxid,m=u?(null==(s=t.fromUser)?void 0:s.includes("@chatroom"))?t.fromUser:t.toUser:h?t.toUser:t.fromUser,1e4===t.msgType&&(m=t.fromUser===e.wxid?t.toUser:t.fromUser);const g={id:(null==(i=t.msgId)?void 0:i.toString())||`msg_${Date.now()}_${Math.random()}`,content:t.content||"",timestamp:t.createTime?new Date(1e3*t.createTime):new Date,fromMe:h,type:this.getMsgType(t.msgType,t.contentType,t.extraData),status:"received",sessionId:m,isGroupMessage:u};if(10002===t.msgType){let e="[系统消息]";if(t.originalContent)try{const a=t.originalContent.match(/<replacemsg><!\[CDATA\[(.*?)\]\]><\/replacemsg>/);if(a&&a[1])e=a[1];else{const a=t.originalContent.match(/\[CDATA\[(.*?)\]\]/);a&&a[1]&&(e=a[1])}}catch(f){}return g.content=e,g.type="system",g.fromMe=!1,t.extraData&&(g.extraData=t.extraData),void this.emit("chat_message",g)}if(u?(g.actualSender=t.actualSender,g.actualSenderName=t.actualSenderName,g.groupId=t.toUser):(g.actualSender=h?e.wxid:t.fromUser,g.actualSenderName=h?t.toUserName:t.fromUserName),3===t.msgType){if(g.content="[图片]",t.originalContent){const e=function(e){const t={};if(!e)return t;try{const a=e.match(/aeskey\s*=\s*"([^"]+)"/);a&&(t.aesKey=a[1]);const n=e.match(/md5\s*=\s*"([^"]+)"/);n&&(t.md5=n[1]);let s=0;const i=e.match(/<img[^>]*\blength\s*=\s*"([^"]+)"/);if(i)s=parseInt(i[1],10);else{const t=e.match(/datasize\s*=\s*"([^"]+)"/);if(t)s=parseInt(t[1],10);else{const t=e.match(/size\s*=\s*"([^"]+)"/);t&&(s=parseInt(t[1],10))}}s>0&&(t.dataLen=s);const o=e.match(/compresstype\s*=\s*"([^"]+)"/);o&&(t.compressType=parseInt(o[1],10)),t.aesKey&&(t.cdnFileAesKey=t.aesKey);const r=e.match(/cdnthumburl\s*=\s*"([^"]+)"/);r&&(t.cdnThumbUrl=r[1],t.cdnFileNo=r[1]);const l=e.match(/cdnmidimgurl\s*=\s*"([^"]+)"/);l&&(t.cdnMidUrl=l[1])}catch(f){}return t}(t.originalContent);g.imageAesKey=e.aesKey,g.imageMd5=e.md5,g.imageDataLen=e.dataLen,g.imageCompressType=e.compressType,g.imageCdnFileAesKey=e.cdnFileAesKey,g.imageCdnFileNo=e.cdnFileNo,g.imageCdnThumbUrl=e.cdnThumbUrl,g.imageCdnMidUrl=e.cdnMidUrl}t.content&&(t.content.startsWith("/")||t.content.includes("\\")?(g.imageData=null,g.imagePath=t.content):(t.content.startsWith("data:image/")||t.content.startsWith("http"),g.imageData=t.content)),t.imageUrl&&(g.imageData=t.imageUrl),t.imageBase64&&(g.imageData=t.imageBase64)}if(6===t.msgType&&t.content&&(g.fileData={name:t.fileName||"未知文件",size:t.fileSize||0,path:t.content},g.content="[文件]"),49===t.msgType)if("file"===t.contentType||t.extraData&&"file"===t.extraData.type){const e=(null==(o=t.extraData)?void 0:o.title)||"未知文件",a=(null==(r=t.extraData)?void 0:r.fileExt)||"";let n=0,s="",i="",l="";if(t.originalContent)try{const e=t.originalContent.match(/<totallen>(\d+)<\/totallen>/);e&&(n=parseInt(e[1]));const a=t.originalContent.match(/<cdnattachurl>(.*?)<\/cdnattachurl>/);a&&(s=a[1]);const o=t.originalContent.match(/<aeskey>(.*?)<\/aeskey>/);o&&(i=o[1]);const r=t.originalContent.match(/<attachid>(.*?)<\/attachid>/);r&&(l=r[1])}catch(f){}if(g.fileData={name:e,size:n,ext:a,cdnUrl:s,aesKey:i,attachId:l,originalContent:t.originalContent},g.content=`[文件: ${e}]`,n>0&&l&&t.originalContent)try{const a=t.originalContent.match(/<appmsg appid="([^"]*)"/),o=a?a[1]:"";B.addFileToCache({fileName:e,fileSize:n,originalContent:t.originalContent,attachId:l,cdnUrl:s,aesKey:i,appId:o})}catch(f){}}else if("link"===t.contentType||t.extraData&&"link"===t.extraData.type){const e=(null==(l=t.extraData)?void 0:l.title)||t.content||"链接",a=(null==(c=t.extraData)?void 0:c.url)||"",n=(null==(d=t.extraData)?void 0:d.thumbUrl)||"";g.linkData={title:e,url:a,thumbUrl:n,originalContent:t.originalContent},g.content=e}else g.content=t.content||"[小程序/其他]";if(43===t.msgType&&(g.content="[视频]",t.originalContent)){const e=function(e){const t={};if(!e)return t;try{const a=e.match(/aeskey\s*=\s*"([^"]+)"/);a&&(t.aesKey=a[1]);const n=e.match(/md5\s*=\s*"([^"]+)"/);n&&(t.md5=n[1]);const s=e.match(/newmd5\s*=\s*"([^"]+)"/);s&&(t.newMd5=s[1]);const i=e.match(/length\s*=\s*"([^"]+)"/);i&&(t.dataLen=parseInt(i[1],10));const o=e.match(/playlength\s*=\s*"([^"]+)"/);o&&(t.playLength=parseInt(o[1],10));const r=e.match(/cdnvideourl\s*=\s*"([^"]+)"/);r&&(t.cdnVideoUrl=r[1]);const l=e.match(/cdnthumburl\s*=\s*"([^"]+)"/);l&&(t.cdnThumbUrl=l[1]);const c=e.match(/cdnthumbaeskey\s*=\s*"([^"]+)"/);c&&(t.cdnThumbAesKey=c[1]);const d=e.match(/cdnthumblength\s*=\s*"([^"]+)"/);d&&(t.cdnThumbLength=parseInt(d[1],10));const u=e.match(/cdnthumbwidth\s*=\s*"([^"]+)"/);u&&(t.cdnThumbWidth=parseInt(u[1],10));const m=e.match(/cdnthumbheight\s*=\s*"([^"]+)"/);m&&(t.cdnThumbHeight=parseInt(m[1],10));const h=e.match(/fromusername\s*=\s*"([^"]+)"/);h&&(t.fromUserName=h[1])}catch(f){}return t}(t.originalContent);g.videoAesKey=e.aesKey,g.videoMd5=e.md5,g.videoNewMd5=e.newMd5,g.videoDataLen=e.dataLen,g.videoCompressType=e.compressType||0,g.videoPlayLength=e.playLength,g.videoCdnUrl=e.cdnVideoUrl,g.videoThumbUrl=e.cdnThumbUrl,g.videoThumbAesKey=e.cdnThumbAesKey,g.videoThumbLength=e.cdnThumbLength,g.videoThumbWidth=e.cdnThumbWidth,g.videoThumbHeight=e.cdnThumbHeight,g.videoFromUserName=e.fromUserName}if(47===t.msgType&&(g.content="[表情]",t.originalContent)){g.emojiData=t.originalContent;const e=t.originalContent.match(/cdnurl\s*=\s*"([^"]+)"/);if(e){const t=e[1].replace(/&amp;/g,"&");g.emojiUrl=t}const a=t.originalContent.match(/thumburl\s*=\s*"([^"]*)"/);if(a&&a[1]&&a[1].trim()){const e=a[1].replace(/&amp;/g,"&");g.emojiThumbUrl=e}const n=t.originalContent.match(/encrypturl\s*=\s*"([^"]+)"/);if(n&&n[1]){const e=n[1].replace(/&amp;/g,"&");g.emojiThumbUrl||(g.emojiThumbUrl=e)}const s=t.originalContent.match(/externurl\s*=\s*"([^"]+)"/);if(s&&s[1]){const e=s[1].replace(/&amp;/g,"&");g.emojiThumbUrl||(g.emojiThumbUrl=e),g.emojiExternUrl=e}const i=t.originalContent.match(/width\s*=\s*"([^"]+)"/),o=t.originalContent.match(/height\s*=\s*"([^"]+)"/);i&&o&&(g.emojiWidth=parseInt(i[1]),g.emojiHeight=parseInt(o[1]));const r=t.originalContent.match(/aeskey\s*=\s*"([^"]+)"/),l=t.originalContent.match(/md5\s*=\s*"([^"]+)"/);r&&l&&(g.emojiAesKey=r[1],g.emojiMd5=l[1])}if(37!==t.msgType){if(1e4===t.msgType){let e=t.originalContent||t.content||"[系统消息]";if(e.includes("你已添加了")||e.includes("通过了你的朋友验证请求"))if(e.includes("你已添加了")){const t=e.match(/你已添加了(.+?)，/);t&&t[1]&&(e=`你已添加了 ${t[1]} 为好友`)}else if(e.includes("通过了你的朋友验证请求")){const t=e.match(/(.+?)通过了你的朋友验证请求/);t&&t[1]&&(e=`${t[1]} 通过了你的好友申请`)}g.content=e,g.fromMe=!1,t.extraData&&(g.extraData=t.extraData)}this.emit("chat_message",g)}else if(t.originalContent)try{const a=new DOMParser,n=a.parseFromString(t.originalContent,"text/xml").querySelector("msg");if(n){const t={fromUserName:n.getAttribute("fromusername")||"",fromNickName:n.getAttribute("fromnickname")||"",content:n.getAttribute("content")||"",alias:n.getAttribute("alias")||"",bigHeadImgUrl:n.getAttribute("bigheadimgurl")||"",smallHeadImgUrl:n.getAttribute("smallheadimgurl")||"",ticket:n.getAttribute("ticket")||"",sex:n.getAttribute("sex")||"0",province:n.getAttribute("province")||"",city:n.getAttribute("city")||"",country:n.getAttribute("country")||""};this.emit("friend_request",{type:"friend_request",data:t,timestamp:new Date,wxid:e.wxid})}}catch(f){}})}shouldFilterMessage(e){if(51===e.msgType||"status"===e.contentType)return!0;if(37===e.msgType)return!1;return[51].includes(e.msgType)}getMsgType(e,t,a){switch(e){case 1:default:return"text";case 3:return"image";case 6:return"file";case 43:return"video";case 47:return"emoji";case 49:return"file"===t||a&&"file"===a.type?"file":("link"===t||a&&a.type,"link");case 1e4:case 10002:return"system"}}startHeartbeat(e){const t=this.connections.get(e);t&&(t.heartbeatInterval=window.setInterval(()=>{if(t.ws&&t.ws.readyState===WebSocket.OPEN){const a={type:"heartbeat",timestamp:Date.now(),wxid:e};t.ws.send(JSON.stringify(a))}},se.HEARTBEAT.INTERVAL))}stopHeartbeat(e){const t=this.connections.get(e);t&&t.heartbeatInterval&&(clearInterval(t.heartbeatInterval),t.heartbeatInterval=null)}scheduleReconnect(e){const t=this.connections.get(e);t&&(t.reconnectAttempts++,setTimeout(()=>{t.reconnectAttempts<=this.maxReconnectAttempts&&this.connect(e)},this.reconnectInterval*t.reconnectAttempts))}get isConnected(){if(!this.currentWxid)return!1;const e=this.connections.get(this.currentWxid);return void 0!==e&&e.ws.readyState===WebSocket.OPEN}isAccountConnected(e){const t=this.connections.get(e);return void 0!==t&&t.ws.readyState===WebSocket.OPEN}send(e){return!!this.currentWxid&&this.sendToAccount(this.currentWxid,e)}sendToAccount(e,t){const a=this.connections.get(e);return!(!a||a.ws.readyState!==WebSocket.OPEN)&&(a.ws.send(JSON.stringify(t)),!0)}};c(ie,"instance",null);const oe=ie.getInstance(),re=D("chat",()=>{const e=S([]),t=S(null),a=S({}),n=S(!1),s=S(!1),i="wechat_chat_sessions",o="wechat_chat_messages",c="wechat_current_session",m=(e,t,a)=>{try{const n=a?`${e}_${a}`:e,s=JSON.stringify(t,(e,t)=>t instanceof Date?{__type:"Date",value:t.toISOString()}:t);localStorage.setItem(n,s)}catch(n){}},h=(e,t)=>{try{const a=t?`${e}_${t}`:e,n=localStorage.getItem(a);if(n)return JSON.parse(n,(e,t)=>{if(t&&"object"==typeof t&&"Date"===t.__type){const e=new Date(t.value);return isNaN(e.getTime())?new Date:e}if(("timestamp"===e||"lastMessageTime"===e)&&t){const e=new Date(t);return Number.isNaN(e.getTime())?new Date:e}return t})}catch(a){}return null},g=I(()=>t.value&&a.value[t.value.id]||[]),f=I(()=>e.value.reduce((e,t)=>e+t.unreadCount,0)),p=t=>{const n=h(i,t);n&&Array.isArray(n)&&(e.value=n);const s=h(o,t);if(s&&"object"==typeof s){a.value=s;Object.values(s).reduce((e,t)=>e+t.length,0)}},v=n=>{if(n)try{m(i,e.value,n),m(o,a.value,n),t.value&&m(c,t.value,n)}catch(s){}},y=a=>{var n;const s=e.value.find(e=>e.id===a);if(s){t.value=s,s.unreadCount=0,w(a);const e=Q();(null==(n=e.currentAccount)?void 0:n.wxid)&&v(e.currentAccount.wxid)}},w=e=>{var t;const n=Q();if(!(null==(t=n.currentAccount)?void 0:t.wxid))return;const s=h(o,n.currentAccount.wxid);if(s&&s[e]){const t=(s[e]||[]).slice(-50);a.value[e]||(a.value[e]=[]),a.value[e]=t}else a.value[e]||(a.value[e]=[])},C=(n,s)=>{var i,o;a.value[n]||(a.value[n]=[]);if(a.value[n].find(e=>e.id===s.id))return;a.value[n].push(s),a.value[n].sort((e,t)=>(e.timestamp instanceof Date?e.timestamp.getTime():new Date(e.timestamp).getTime())-(t.timestamp instanceof Date?t.timestamp.getTime():new Date(t.timestamp).getTime()));const r=e.value.findIndex(e=>e.id===n);if(-1!==r){const a=e.value[r];a.lastMessage=s.content,a.lastMessageTime=s.timestamp,!1===s.fromMe&&(null==(i=t.value)?void 0:i.id)!==n&&a.unreadCount++,r>0&&(e.value.splice(r,1),e.value.unshift(a))}const l=Q();(null==(o=l.currentAccount)?void 0:o.wxid)&&v(l.currentAccount.wxid)},x=(e,t,a)=>d(null,null,function*(){s.value=!0;const n=Date.now().toString(),i=Date.now(),o={id:n,content:a,timestamp:new Date,fromMe:!0,type:"text",status:"sending",originalContent:a,canRetry:!1,canRecall:!1,retryCount:0,sessionId:t,isGroupMessage:t.includes("@chatroom"),actualSender:e,clientMsgId:parseInt(n),createTime:Math.floor(i/1e3),newMsgId:parseInt(n)};C(t,o);try{const s=yield Z({Wxid:e,ToWxid:t,Content:a,Type:1});return s.Success?D(t,n,"sent",!1,s):D(t,n,"failed",!0),s}catch(r){throw D(t,n,"failed",!0),r}finally{s.value=!1}}),T=(e,t,a)=>d(null,null,function*(){s.value=!0;const n=Date.now().toString(),i=Date.now(),o={id:n,content:"[图片]",timestamp:new Date,fromMe:!0,type:"image",imageData:a,status:"sending",sessionId:t,isGroupMessage:t.includes("@chatroom"),actualSender:e,canRecall:!1,clientMsgId:parseInt(n),createTime:Math.floor(i/1e3),newMsgId:parseInt(n)};C(t,o);try{const s=yield Y({Wxid:e,ToWxid:t,Base64:a});return s.Success?D(t,n,"sent",!1,s):D(t,n,"failed",!0),s}catch(r){throw D(t,n,"failed",!0),r}finally{s.value=!1}}),b=(e,t,a)=>d(null,null,function*(){s.value=!0;const n=Date.now().toString(),i=Date.now();let o="未知文件",r=0,l="",c="",d="";try{const e=a.match(/<title>(.*?)<\/title>/);e&&(o=e[1]);const t=a.match(/<totallen>(\d+)<\/totallen>/);t&&(r=parseInt(t[1]));const n=a.match(/<attachid>(.*?)<\/attachid>/);n&&(l=n[1]);const s=a.match(/<cdnurl>(.*?)<\/cdnurl>/);s&&(c=s[1]);const i=a.match(/<aeskey>(.*?)<\/aeskey>/);i&&(d=i[1])}catch(m){}const u={id:n,content:`[文件] ${o}`,timestamp:new Date,fromMe:!0,type:"file",status:"sending",sessionId:t,isGroupMessage:t.includes("@chatroom"),actualSender:e,canRecall:!1,clientMsgId:parseInt(n),createTime:Math.floor(i/1e3),newMsgId:parseInt(n),fileData:{name:o,size:r,originalContent:a,attachId:l,cdnUrl:c,aesKey:d}};C(t,u);try{const s=yield ae({Wxid:e,ToWxid:t,Content:a});return s.Success?D(t,n,"sent",!1,s):D(t,n,"failed",!0),s}catch(m){throw D(t,n,"failed",!0),m}finally{s.value=!1}}),D=(e,t,n,s=!1,i)=>{const o=a.value[e];if(o){const e=o.find(e=>e.id===t);if(e&&(e.status=n,e.canRetry=s&&"failed"===n,e.canRecall="sent"===n&&e.fromMe,i&&i.Data&&i.Data.List&&i.Data.List.length>0)){const t=i.Data.List[0];e.clientMsgId=t.ClientMsgid,e.createTime=t.Createtime,e.newMsgId=t.NewMsgId}}},A=()=>{e.value=[],t.value=null,a.value={}},M=a=>d(null,null,function*(){var n;const s=a.sessionId||(a.fromMe?a.toUser:a.fromUser),i={id:a.id||Date.now().toString(),content:a.content||"",timestamp:a.timestamp instanceof Date?a.timestamp:new Date(a.timestamp||Date.now()),fromMe:a.fromMe||!1,type:a.type||"text",status:"received",sessionId:s,isGroupMessage:(null==s?void 0:s.includes("@chatroom"))||!1,actualSender:a.actualSender||a.fromUser,actualSenderName:a.actualSenderName||a.senderName,emojiUrl:a.emojiUrl,emojiThumbUrl:a.emojiThumbUrl,emojiExternUrl:a.emojiExternUrl,emojiWidth:a.emojiWidth,emojiHeight:a.emojiHeight,emojiData:a.emojiData,emojiAesKey:a.emojiAesKey,emojiMd5:a.emojiMd5,imageData:a.imageData,imagePath:a.imagePath,imageAesKey:a.imageAesKey,imageMd5:a.imageMd5,imageDataLen:a.imageDataLen,imageCompressType:a.imageCompressType,imageCdnFileAesKey:a.imageCdnFileAesKey,imageCdnFileNo:a.imageCdnFileNo,imageCdnThumbUrl:a.imageCdnThumbUrl,imageCdnMidUrl:a.imageCdnMidUrl,videoAesKey:a.videoAesKey,videoMd5:a.videoMd5,videoNewMd5:a.videoNewMd5,videoDataLen:a.videoDataLen,videoCompressType:a.videoCompressType,videoPlayLength:a.videoPlayLength,videoCdnUrl:a.videoCdnUrl,videoThumbUrl:a.videoThumbUrl,videoThumbAesKey:a.videoThumbAesKey,videoThumbLength:a.videoThumbLength,videoThumbWidth:a.videoThumbWidth,videoThumbHeight:a.videoThumbHeight,videoFromUserName:a.videoFromUserName,fileData:a.fileData,extraData:a.extraData};if(s){let a=e.value.find(e=>e.id===s);if(!a){a={id:s,name:s,avatar:"",type:"friend",lastMessage:"",lastMessageTime:new Date,unreadCount:0,isOnline:!1},e.value.unshift(a);const i=Q();(null==(n=i.currentAccount)?void 0:n.wxid)&&(o=i.currentAccount.wxid,c=s,d(null,null,function*(){var e,t,a,n,s,i,r;try{const l=c.includes("@chatroom"),d=yield J.getFriendDetail({Wxid:o,Towxids:c,ChatRoom:l?c:"",force_refresh:!1});if(d.Success&&d.Data&&d.Data.length>0){const o=d.Data[0];if(l){let s=c;return s=d.Data[0].ContactList&&d.Data[0].ContactList.length>0?(null==(e=d.Data[0].ContactList[0].NickName)?void 0:e.string)||c:(null==(t=o.NickName)?void 0:t.string)||(null==(a=o.Remark)?void 0:a.string)||c,{wxid:c,nickname:s,alias:o.Alias||"",avatar:o.SmallHeadImgUrl||o.BigHeadImgUrl||"",remark:(null==(n=o.Remark)?void 0:n.string)||"",isGroup:l}}return{wxid:c,nickname:(null==(s=o.NickName)?void 0:s.string)||(null==(i=o.Remark)?void 0:i.string)||c,alias:o.Alias||"",avatar:o.SmallHeadImgUrl||o.BigHeadImgUrl||"",remark:(null==(r=o.Remark)?void 0:r.string)||"",isGroup:l}}}catch(l){}return null})).then(a=>{var n;if(a){const o=e.value.findIndex(e=>e.id===s);if(-1!==o){const c=l(r({},e.value[o]),{name:a.isGroup?a.nickname||s:a.remark||a.nickname||a.alias||s,type:a.isGroup?"group":"friend",avatar:a.avatar||""});e.value[o]=c,(null==(n=t.value)?void 0:n.id)===s&&(t.value=c),v(i.currentAccount.wxid)}}})}C(s,i),t.value||y(s)}var o,c}),_=e=>{e.message},L=(a,n)=>{var s,i;const o=e.value.findIndex(e=>e.id===a);if(-1!==o){const l=r(r({},e.value[o]),n);e.value[o]=l,(null==(s=t.value)?void 0:s.id)===a&&(t.value=l);const c=Q();return(null==(i=c.currentAccount)?void 0:i.wxid)&&v(c.currentAccount.wxid),l}return null};return{sessions:e,currentSession:t,messages:a,isLoading:n,isSending:s,currentMessages:g,unreadCount:f,setSessions:t=>{var a;e.value=t;const n=Q();(null==(a=n.currentAccount)?void 0:a.wxid)&&v(n.currentAccount.wxid)},setCurrentSession:y,addMessage:C,loadMessages:e=>d(null,null,function*(){if(!a.value[e]){n.value=!0;try{a.value[e]=[]}catch(t){a.value[e]=[]}finally{n.value=!1}}}),sendTextMessage:x,sendImageMessage:T,sendFileMessage:(e,t,a)=>d(null,null,function*(){s.value=!0;try{const s=yield B.findCachedFile(a);if(s)return u.success("检测到相同文件，使用快速发送模式"),yield b(e,t,s.originalContent);if(a.type.startsWith("image/"))try{const n=new FileReader,s=new Promise((e,t)=>{n.onload=()=>e(n.result),n.onerror=t});n.readAsDataURL(a);const i=yield s;return yield T(e,t,i)}catch(n){throw new Error("图片文件处理失败")}const i=Date.now().toString(),o=Date.now(),r={id:i,content:`[文件] ${a.name}`,timestamp:new Date,fromMe:!0,type:"file",fileData:{name:a.name,size:a.size,url:URL.createObjectURL(a)},status:"sending",sessionId:t,isGroupMessage:t.includes("@chatroom"),actualSender:e,canRecall:!1,clientMsgId:parseInt(i),createTime:Math.floor(o/1e3),newMsgId:parseInt(i)};C(t,r);try{D(t,i,"failed",!1);const e=`无法发送文件 "${a.name}"。\n\n原因：\n1. 该文件不是图片格式\n2. 未找到该文件的缓存信息\n\n解决方案：\n• 如果要发送图片，请使用 JPG、PNG 等图片格式\n• 如果要发送其他文件，请先在微信中接收过相同的文件，然后再尝试发送`;throw new Error(e)}catch(n){throw D(t,i,"failed",!0),n}}catch(n){throw n}finally{s.value=!1}}),sendCachedFileMessage:b,clearMessages:e=>{var t;delete a.value[e];const n=Q();if(null==(t=n.currentAccount)?void 0:t.wxid){const t=n.currentAccount.wxid,a=h(o,t)||{};delete a[e],m(o,a,t);h(o,t)}},updateMessageStatus:D,retryMessage:(e,t,n)=>d(null,null,function*(){const s=a.value[t];if(!s)return;const i=s.find(e=>e.id===n);if(!i||!i.canRetry||!i.originalContent)return;const o=s.findIndex(e=>e.id===n);-1!==o&&s.splice(o,1);try{yield x(e,t,i.originalContent)}catch(r){}}),recallMessage:(e,t,n)=>d(null,null,function*(){const s=a.value[t];if(!s)return;const i=s.find(e=>e.id===n);if(i&&i.fromMe)try{const a=yield te({Wxid:e,ToUserName:t,ClientMsgId:i.clientMsgId||parseInt(i.id)||Date.now(),CreateTime:i.createTime||Math.floor(i.timestamp.getTime()/1e3),NewMsgId:i.newMsgId||parseInt(i.id)||Date.now()});if(!a.Success)throw new Error(a.Message||"撤回消息失败");{const e=s.findIndex(e=>e.id===n);-1!==e&&s.splice(e,1)}}catch(o){throw o}}),clearAllData:A,connectWebSocket:e=>d(null,null,function*(){try{if(oe.isAccountConnected(e))return oe.switchCurrentAccount(e),!0;oe.on("chat_message",M),oe.on("system_message",_);const t=yield oe.connect(e);return t}catch(t){return!1}}),disconnectWebSocket:()=>{oe.disconnect()},createOrGetSession:t=>{let a=e.value.find(e=>e.id===t.wxid);return a||(a={id:t.wxid,name:t.remark||t.nickname||t.alias||"未知好友",avatar:t.avatar||"",type:"friend",lastMessage:"",lastMessageTime:new Date,unreadCount:0,isOnline:t.isOnline||!1},e.value.unshift(a)),a},syncMessages:e=>d(null,null,function*(){try{yield ee(e)}catch(t){}}),testWebSocketMessage:()=>{const e={id:`test_${Date.now()}`,content:"这是一条测试消息",timestamp:new Date,fromMe:!1,type:"text",sessionId:"test_session"};M(e)},updateSessionInfo:L,removeSession:n=>{var s,i;const o=e.value.findIndex(e=>e.id===n);if(-1!==o){e.value.splice(o,1),delete a.value[n],(null==(s=t.value)?void 0:s.id)===n&&(t.value=null);const r=Q();(null==(i=r.currentAccount)?void 0:i.wxid)&&v(r.currentAccount.wxid)}},updateSessionName:(e,t)=>{L(e,{name:t})},loadCachedData:p,saveCachedData:v,switchAccount:(e,t)=>{t&&v(t),A(),e&&p(e)},clearCache:e=>{try{e?(localStorage.removeItem(`${i}_${e}`),localStorage.removeItem(`${o}_${e}`),localStorage.removeItem(`${c}_${e}`)):Object.keys(localStorage).forEach(e=>{e.startsWith("wechat_chat_")&&localStorage.removeItem(e)})}catch(t){}}}}),le=Object.freeze(Object.defineProperty({__proto__:null,useChatStore:re},Symbol.toStringTag,{value:"Module"})),ce=(e,t)=>{const a=e.__vccOpts||e;for(const[n,s]of t)a[n]=s;return a},de={class:"image-message-wrapper"},ue={key:0,class:"image-loading"},me={key:1,class:"image-error"},he={key:2,class:"image-success"},ge={class:"image-error"},fe={key:3,class:"image-placeholder"},pe="wechat_image_cache_",ve=ce(A({__name:"ImageMessage",props:{msgId:{},wxid:{},toWxid:{},aesKey:{},md5:{},dataLen:{},compressType:{},imageData:{},imagePath:{},cdnFileAesKey:{},cdnFileNo:{}},setup(e){const t=new Map,a=()=>{try{const t=Date.now(),a=[];for(let n=0;n<localStorage.length;n++){const s=localStorage.key(n);if(s&&s.startsWith(pe))try{const e=localStorage.getItem(s);if(e){t-JSON.parse(e).timestamp>=6048e5&&a.push(s)}}catch(e){a.push(s)}}a.forEach(e=>localStorage.removeItem(e))}catch(t){}},n=e;re();const s=S(!1),i=S(!1),o=S(""),r=I(()=>n.imageData&&(n.imageData.startsWith("data:image/")||n.imageData.startsWith("http://")||n.imageData.startsWith("https://")));M(()=>{Math.random()<.1&&a(),r.value?o.value=n.imageData:c()});const l=I(()=>n.md5?`md5_${n.md5}`:n.msgId&&n.wxid?`msg_${n.wxid}_${n.toWxid}_${n.msgId}`:n.cdnFileNo?`cdn_${n.cdnFileNo}`:`temp_${Date.now()}`),c=()=>d(null,null,function*(){if(!n.wxid)return void(i.value=!0);const e=(e=>{try{if(t.has(e))return t.get(e);const a=pe+e,n=localStorage.getItem(a);if(n){const s=JSON.parse(n);if(Date.now()-s.timestamp<6048e5)return t.set(e,s.data),s.data;localStorage.removeItem(a)}}catch(a){}return null})(l.value);if(e)o.value=e;else{s.value=!0,i.value=!1;try{let e=null;if(n.cdnFileAesKey&&n.cdnFileNo)try{e=yield ne({Wxid:n.wxid,FileAesKey:n.cdnFileAesKey,FileNo:n.cdnFileNo})}catch(c){e=null}if(!e&&n.msgId&&n.toWxid&&n.dataLen){const t={Wxid:n.wxid,ToWxid:n.toWxid,MsgId:n.msgId,DataLen:n.dataLen,CompressType:n.compressType||0,Section:{StartPos:0,DataLen:n.dataLen}};e=yield(r=t,q.post("/Tools/DownloadImg",r))}if(!e)throw new Error("所有下载方案都失败了");let s="";if(!0===e.Success&&e.Data&&e.Data.Image)s=e.Data.Image;else if(!0===e.Success&&e.Data&&"string"==typeof e.Data)s=e.Data;else if(e.Success&&e.Data)s=e.Data.buffer?e.Data.buffer:e.Data.data&&e.Data.data.buffer?e.Data.data.buffer:e.Data;else{if(!e.data)throw new Error("API返回空数据或格式不正确");s=e.data.buffer?e.data.buffer:e.data}if(!(s&&s.length>0))throw new Error("API返回空数据");{let e="";if(s.startsWith("data:image/"))e=s;else{e=`data:${u(s)};base64,${s}`}o.value=e,((e,n)=>{try{t.set(e,n);const a=pe+e,s={data:n,timestamp:Date.now()};localStorage.setItem(a,JSON.stringify(s))}catch(s){a()}})(l.value,e)}}catch(d){i.value=!0}finally{s.value=!1}var r}}),u=e=>e.startsWith("/9j/")?"image/jpeg":e.startsWith("iVBORw0KGgo")?"image/png":e.startsWith("R0lGOD")?"image/gif":e.startsWith("UklGR")?"image/webp":"image/jpeg",p=()=>{},v=()=>{i.value=!0};return(e,t)=>{const a=m,n=f;return L(),_("div",de,[s.value?(L(),_("div",ue,[U(a,{class:"is-loading"},{default:W(()=>[U(N(h))]),_:1}),t[0]||(t[0]=k("div",{style:{"font-size":"10px","margin-top":"4px"}},"图片加载中...",-1))])):i.value?(L(),_("div",me,[U(a,{class:"image-error-icon"},{default:W(()=>[U(N(g))]),_:1}),t[1]||(t[1]=k("span",{class:"image-error-text"},"[图片]",-1)),t[2]||(t[2]=k("div",{style:{"font-size":"10px","margin-top":"4px"}},"加载失败",-1))])):o.value?(L(),_("div",he,[U(n,{src:o.value,fit:"contain","preview-src-list":[o.value],class:"image-content","hide-on-click-modal":!0,lazy:!1,onLoad:p,onError:v},{error:W(()=>[k("div",ge,[U(a,{class:"image-error-icon"},{default:W(()=>[U(N(g))]),_:1}),t[3]||(t[3]=k("span",{class:"image-error-text"},"图片加载失败",-1))])]),_:1},8,["src","preview-src-list"])])):(L(),_("div",fe,[U(a,{class:"image-placeholder-icon"},{default:W(()=>[U(N(g))]),_:1}),t[4]||(t[4]=k("span",{class:"image-placeholder-text"},"图片",-1))]))])}}}),[["__scopeId","data-v-3be6e853"]]),ye={class:"video-message-wrapper"},we={key:0,class:"video-loading"},Ce={key:1,class:"video-error"},xe={key:2,class:"video-success"},Te=["src"],be={key:0,class:"video-duration"},De={key:3,class:"video-placeholder"},Se="wechat_video_cache_",Ie=ce(A({__name:"VideoMessage",props:{msgId:{},wxid:{},toWxid:{},aesKey:{},md5:{},newMd5:{},dataLen:{},compressType:{},playLength:{},cdnVideoUrl:{},fromUserName:{},videoData:{},videoPath:{}},setup(e){const t=e;re();const a=S(!1),n=S(!1),s=S(""),i=I(()=>t.videoData&&(t.videoData.startsWith("data:video/")||t.videoData.startsWith("http://")||t.videoData.startsWith("https://"))),o=I(()=>t.md5?`md5_${t.md5}`:t.newMd5?`newmd5_${t.newMd5}`:t.msgId&&t.wxid?`msg_${t.wxid}_${t.toWxid}_${t.msgId}`:`temp_${Date.now()}`);M(()=>{i.value?s.value=t.videoData:r()});const r=()=>d(null,null,function*(){if(!t.wxid)return void(n.value=!0);const e=(e=>{try{const t=Se+e,a=localStorage.getItem(t);if(a){const e=JSON.parse(a);if(Date.now()-e.timestamp<2592e5)return e.data;localStorage.removeItem(t)}}catch(t){}return null})(o.value);if(e)s.value=e;else{a.value=!0,n.value=!1;try{if(!t.msgId||!t.toWxid||!t.dataLen)throw new Error("缺少必需的下载参数");const e={Wxid:t.wxid,ToWxid:t.toWxid,MsgId:t.msgId,DataLen:t.dataLen,CompressType:t.compressType||0,Section:{StartPos:0,DataLen:t.dataLen}},a=yield(i=e,q.post("/Tools/DownloadVideo",i));let n="";if(!(!0===a.Success&&a.Data&&a.Data.data&&a.Data.data.buffer))throw new Error("API返回空数据或格式不正确");if(n=a.Data.data.buffer,!(n&&n.length>0))throw new Error("API返回空数据");{let e="";e=n.startsWith("data:video/")?n:`data:video/mp4;base64,${n}`,s.value=e,((e,t)=>{try{const a=Se+e,n={data:t,timestamp:Date.now()};localStorage.setItem(a,JSON.stringify(n))}catch(a){}})(o.value,e)}}catch(r){n.value=!0}finally{a.value=!1}var i}}),l=e=>{const t=e%60;return`${Math.floor(e/60).toString().padStart(2,"0")}:${t.toString().padStart(2,"0")}`},c=()=>{},u=()=>{},g=()=>{n.value=!0};return(e,t)=>{const i=m;return L(),_("div",ye,[a.value?(L(),_("div",we,[U(i,{class:"is-loading"},{default:W(()=>[U(N(h))]),_:1}),t[0]||(t[0]=k("div",{style:{"font-size":"10px","margin-top":"4px"}},"视频加载中...",-1))])):n.value?(L(),_("div",Ce,[U(i,{class:"video-error-icon"},{default:W(()=>[U(N(p))]),_:1}),t[1]||(t[1]=k("span",{class:"video-error-text"},"[视频]",-1)),t[2]||(t[2]=k("div",{style:{"font-size":"10px","margin-top":"4px"}},"加载失败",-1))])):s.value?(L(),_("div",xe,[k("video",{src:s.value,controls:"",preload:"metadata",class:"video-content",onLoadstart:c,onLoadeddata:u,onError:g}," 您的浏览器不支持视频播放 ",40,Te),e.playLength?(L(),_("div",be,F(l(e.playLength)),1)):E("",!0)])):(L(),_("div",De,[U(i,{class:"video-placeholder-icon"},{default:W(()=>[U(N(p))]),_:1}),t[3]||(t[3]=k("span",{class:"video-placeholder-text"},"视频",-1))]))])}}}),[["__scopeId","data-v-22789787"]]),Ae={class:"file-message-wrapper"},Me={class:"file-container"},_e={class:"file-icon"},Le={class:"file-info"},Ue=["title"],ke={class:"file-size"},We={class:"file-actions"},Ne=ce(A({__name:"FileMessage",props:{fileName:{},fileSize:{},fileUrl:{},mimeType:{},cdnUrl:{},aesKey:{},attachId:{},wxid:{},userName:{},appId:{},originalContent:{},messageStatus:{},fromMe:{type:Boolean}},setup(e){const t=e,a=S(!1);re(),Q();const n=I(()=>[".doc",".docx",".pdf",".txt",".rtf",".xls",".xlsx",".ppt",".pptx"].some(e=>t.fileName.toLowerCase().endsWith(e))),s=I(()=>[".mp4",".avi",".mov",".wmv",".flv",".mkv",".webm"].some(e=>t.fileName.toLowerCase().endsWith(e))),i=I(()=>[".jpg",".jpeg",".png",".gif",".bmp",".webp",".svg"].some(e=>t.fileName.toLowerCase().endsWith(e))),o=e=>{if(0===e)return"0 B";const t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["B","KB","MB","GB","TB"][t]},r=()=>d(null,null,function*(){if(t.fileUrl){a.value=!0;try{const e=document.createElement("a");e.href=t.fileUrl,e.download=t.fileName,e.target="_blank",document.body.appendChild(e),e.click(),document.body.removeChild(e),u.success("文件下载已开始")}catch(e){u.error("下载文件失败")}finally{a.value=!1}}else u.warning("文件链接不可用")}),l=()=>d(null,null,function*(){var e,n;if(t.attachId&&t.wxid&&t.userName){a.value=!0;try{const e={Wxid:t.wxid,AppID:t.appId,AttachId:t.attachId,UserName:t.userName,DataLen:t.fileSize,Section:{StartPos:0,DataLen:t.fileSize}};let a;0;try{a=yield X(e)}catch(s){throw s}if(!(a&&a.Success&&a.Data)){const e=(null==a?void 0:a.Message)||"下载失败：服务器响应格式错误";throw new Error(e)}{const e=a.Data.data||a.Data.Data,n=(null==e?void 0:e.buffer)||(null==e?void 0:e.Buffer);if(!n)throw new Error("服务器未返回文件数据");try{const e=atob(n),a=new Array(e.length);for(let t=0;t<e.length;t++)a[t]=e.charCodeAt(t);const s=new Uint8Array(a),i=new Blob([s]),o=URL.createObjectURL(i),r=document.createElement("a");r.href=o,r.download=t.fileName,document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(o),u.success(`文件下载成功 (${i.size} 字节)`)}catch(i){throw new Error("文件数据格式错误")}}}catch(o){let t="下载文件失败";o.message?t=o.message:(null==(n=null==(e=o.response)?void 0:e.data)?void 0:n.Message)&&(t=o.response.data.Message),u.error(t)}finally{a.value=!1}}else u.warning("文件下载参数不完整")});return(e,t)=>{const c=m,d=w;return L(),_("div",Ae,[k("div",Me,[k("div",_e,[U(c,{size:32},{default:W(()=>[n.value?(L(),R(N(v),{key:0})):s.value?(L(),R(N(p),{key:1})):i.value?(L(),R(N(g),{key:2})):(L(),R(N(y),{key:3}))]),_:1})]),k("div",Le,[k("div",{class:"file-name",title:e.fileName},F(e.fileName),9,Ue),k("div",ke,F(o(e.fileSize)),1)]),k("div",We,[e.fileUrl?(L(),R(d,{key:0,type:"primary",size:"small",onClick:r,loading:a.value},{default:W(()=>[U(c,null,{default:W(()=>[U(N(C))]),_:1}),t[0]||(t[0]=O(" 下载 ",-1))]),_:1,__:[0]},8,["loading"])):e.cdnUrl&&e.aesKey&&e.attachId&&!e.fromMe?(L(),R(d,{key:1,type:"warning",size:"small",onClick:l,loading:a.value},{default:W(()=>[U(c,null,{default:W(()=>[U(N(C))]),_:1}),t[1]||(t[1]=O(" 下载 ",-1))]),_:1,__:[1]},8,["loading"])):e.fromMe?e.fromMe&&"sent"===e.messageStatus?(L(),R(d,{key:3,type:"success",size:"small",disabled:""},{default:W(()=>[U(c,null,{default:W(()=>[U(N(x))]),_:1}),t[3]||(t[3]=O(" 已发送 ",-1))]),_:1,__:[3]})):e.fromMe&&"failed"===e.messageStatus?(L(),R(d,{key:4,type:"danger",size:"small",disabled:""},{default:W(()=>[U(c,null,{default:W(()=>[U(N(T))]),_:1}),t[4]||(t[4]=O(" 发送失败 ",-1))]),_:1,__:[4]})):e.fromMe&&"sending"===e.messageStatus?(L(),R(d,{key:5,type:"info",size:"small",disabled:""},{default:W(()=>[U(c,null,{default:W(()=>[U(N(h))]),_:1}),t[5]||(t[5]=O(" 发送中 ",-1))]),_:1,__:[5]})):(L(),R(d,{key:6,type:"info",size:"small",disabled:""},{default:W(()=>[U(c,null,{default:W(()=>[U(N(h))]),_:1}),t[6]||(t[6]=O(" 处理中 ",-1))]),_:1,__:[6]})):(L(),R(d,{key:2,type:"info",size:"small",disabled:""},{default:W(()=>[U(c,null,{default:W(()=>[U(N(v))]),_:1}),t[2]||(t[2]=O(" 文件已接收 ",-1))]),_:1,__:[2]}))])])])}}}),[["__scopeId","data-v-9ecedd29"]]),Ee={class:"base-modal-footer"},Fe=ce(A({__name:"BaseModal",props:{modelValue:{type:Boolean},title:{default:""},width:{default:"50%"},top:{default:"15vh"},appendToBody:{type:Boolean,default:!0},closeOnClickModal:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},showClose:{type:Boolean,default:!0},modal:{type:Boolean,default:!0},lockScroll:{type:Boolean,default:!0},customClass:{default:""},bodyClass:{default:""},showDefaultFooter:{type:Boolean,default:!1},cancelText:{default:"取消"},confirmText:{default:"确定"},confirmLoading:{type:Boolean,default:!1}},emits:["update:modelValue","open","opened","close","closed","cancel","confirm"],setup(e,{emit:t}){const a=e,n=t,s=I({get:()=>a.modelValue,set:e=>n("update:modelValue",e)}),i=()=>{n("open")},o=()=>{n("opened")},r=()=>{n("close"),n("update:modelValue",!1)},l=()=>{n("closed")},c=()=>{n("cancel"),r()},d=()=>{n("confirm")};return(e,t)=>{const a=w,n=b;return L(),R(n,{modelValue:s.value,"onUpdate:modelValue":t[0]||(t[0]=e=>s.value=e),title:e.title,width:e.width,top:e.top,"before-close":r,"append-to-body":e.appendToBody,"close-on-click-modal":e.closeOnClickModal,"close-on-press-escape":e.closeOnPressEscape,"show-close":e.showClose,modal:e.modal,"lock-scroll":e.lockScroll,"custom-class":e.customClass,onOpen:i,onOpened:o,onClose:r,onClosed:l},P({default:W(()=>[k("div",{class:$(["base-modal-body",e.bodyClass])},[j(e.$slots,"default",{},void 0,!0)],2)]),_:2},[e.$slots.header?{name:"header",fn:W(()=>[j(e.$slots,"header",{},void 0,!0)]),key:"0"}:void 0,e.$slots.footer?{name:"footer",fn:W(()=>[j(e.$slots,"footer",{},void 0,!0)]),key:"1"}:e.showDefaultFooter?{name:"footer",fn:W(()=>[k("div",Ee,[U(a,{onClick:c},{default:W(()=>[O(F(e.cancelText),1)]),_:1}),U(a,{type:"primary",loading:e.confirmLoading,onClick:d},{default:W(()=>[O(F(e.confirmText),1)]),_:1},8,["loading"])])]),key:"2"}:void 0]),1032,["modelValue","title","width","top","append-to-body","close-on-click-modal","close-on-press-escape","show-close","modal","lock-scroll","custom-class"])}}}),[["__scopeId","data-v-9441d442"]]);export{Fe as B,Ne as F,ve as I,Ie as V,ce as _,re as a,G as b,z as c,ne as d,q as e,J as f,le as g,V as l,Q as u,oe as w};

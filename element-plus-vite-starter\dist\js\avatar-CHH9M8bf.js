var e=(e,t,n)=>new Promise((s,a)=>{var i=e=>{try{u(n.next(e))}catch(t){a(t)}},r=e=>{try{u(n.throw(e))}catch(t){a(t)}},u=e=>e.done?s(e.value):Promise.resolve(e.value).then(i,r);u((n=n.apply(e,t)).next())});import{av as t,r as n,c as s}from"./vue-C-lRF7aZ.js";import{f as a}from"./components-BbzPJspU.js";const i=2,r=t("friend",()=>{const t=n({}),r=n([]),u=n(!1),c=n(!1),l=n(!1),o=s(()=>e=>t.value[e]||[]),d=s(()=>r.value.filter(e=>"pending"===e.status)),v=(e,n)=>{t.value[e]=n},f=(t,n)=>e(null,null,function*(){c.value=!0;try{return yield a.searchContact({Wxid:t,ToUserName:n,FromScene:0,SearchScene:1})}catch(e){throw e}finally{c.value=!1}}),m=(t,n,s,u,c=3)=>e(null,null,function*(){l.value=!0;try{if(!t||!n||!s)throw new Error("缺少必要参数：wxid、V1、V2不能为空");if(!u.trim())throw new Error("验证消息不能为空");const e=yield a.sendFriendRequest({Wxid:t,V1:n,V2:s,Opcode:i,Scene:c,VerifyContent:u.trim()});if(e.Success){const e={id:Date.now().toString(),wxid:n,nickname:"未知",avatar:"",verifyContent:u.trim(),status:"pending",timestamp:new Date};r.value.push(e)}return e}catch(e){throw e}finally{l.value=!1}});return{friends:t,friendRequests:r,isLoading:u,isSearching:c,isAdding:l,currentFriends:o,pendingRequests:d,setFriends:v,addFriend:(e,n)=>{t.value[e]||(t.value[e]=[]);const s=t.value[e].findIndex(e=>e.wxid===n.wxid);s>=0?t.value[e][s]=n:t.value[e].push(n)},removeFriend:(e,n)=>{if(t.value[e]){const s=t.value[e].findIndex(e=>e.wxid===n);s>=0&&t.value[e].splice(s,1)}},loadFriends:(n,s=!1)=>e(null,null,function*(){var e;if(!t.value[n]||s){u.value=!0;try{const t=yield a.getFriendList({Wxid:n,CurrentWxcontactSeq:0,CurrentChatRoomContactSeq:0,force_refresh:s});if(t.Success&&t.Data){const s=(null==(e=t.Data.ContactList)?void 0:e.map(e=>{var t,n;return{wxid:(null==(t=e.UserName)?void 0:t.string)||"",nickname:(null==(n=e.NickName)?void 0:n.string)||"",avatar:e.SmallHeadImgUrl||"",remark:e.Remark||"",signature:e.Signature||"",sex:e.Sex||0,isOnline:!0}}))||[];v(n,s)}}catch(i){}finally{u.value=!1}}}),searchContact:f,sendFriendRequest:m,batchAddFriends:(t,n)=>e(null,null,function*(){var e;const s=[];for(const i of n)try{yield new Promise(e=>setTimeout(e,2e3));const n=yield f(t,i.identifier);if(n.Success&&n.Data){const a=(null==(e=n.Data.UserName)?void 0:e.string)||"",r=n.Data.AntispamTicket||"";if(a&&r){const e=yield m(t,a,r,i.message);s.push({target:i.identifier,success:e.Success,message:e.Message})}else s.push({target:i.identifier,success:!1,message:"搜索结果中缺少必要参数"})}else s.push({target:i.identifier,success:!1,message:n.Message||"搜索失败"})}catch(a){s.push({target:i.identifier,success:!1,message:a instanceof Error?a.message:"未知错误"})}return s}),updateRequestStatus:(e,t)=>{const n=r.value.find(t=>t.id===e);n&&(n.status=t)}}});export{r as u};
